"""
QUALIA Configuration Hot-reload & Rollback System
================================================

D-05: Sistema robusto de hot-reload e rollback para atualizações em tempo real
sem downtime e reversão segura em caso de problemas.

YAA IMPLEMENTATION: Sistema de hot-reload com file watchers, validação de configurações,
notificação de componentes, versionamento e rollback automático.
"""

from __future__ import annotations

import asyncio
import json
import logging
import os
import shutil
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable, Set
from dataclasses import dataclass, field
from enum import Enum
import hashlib

import yaml
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from ..utils.logger import get_logger

logger = get_logger(__name__)

class ReloadStatus(Enum):
    """Status do hot-reload."""
    SUCCESS = "success"
    FAILED = "failed"
    VALIDATION_ERROR = "validation_error"
    ROLLBACK = "rollback"

@dataclass
class ConfigVersion:
    """Versão de configuração para rollback."""
    version_id: str
    timestamp: datetime
    config_path: str
    config_data: Dict[str, Any]
    backup_path: str
    checksum: str
    description: str = ""

@dataclass
class ReloadEvent:
    """Evento de reload de configuração."""
    timestamp: datetime
    config_path: str
    status: ReloadStatus
    version_before: str
    version_after: Optional[str]
    error_message: Optional[str] = None
    rollback_version: Optional[str] = None

class ConfigFileHandler(FileSystemEventHandler):
    """Handler para eventos de mudança em arquivos de configuração."""
    
    def __init__(self, hot_reloader: 'ConfigurationHotReloader'):
        self.hot_reloader = hot_reloader
        self.debounce_time = 1.0  # 1 segundo de debounce
        self.pending_reloads: Dict[str, float] = {}
        
    def on_modified(self, event):
        """Chamado quando um arquivo é modificado."""
        if event.is_directory:
            return
            
        file_path = event.src_path
        if not self._is_config_file(file_path):
            return
            
        # Debounce para evitar múltiplos reloads
        current_time = time.time()
        if file_path in self.pending_reloads:
            if current_time - self.pending_reloads[file_path] < self.debounce_time:
                return
                
        self.pending_reloads[file_path] = current_time
        
        # Agendar reload após debounce
        threading.Timer(
            self.debounce_time,
            self._trigger_reload,
            args=[file_path]
        ).start()
        
    def _is_config_file(self, file_path: str) -> bool:
        """Verifica se o arquivo é um arquivo de configuração monitorado."""
        return any(
            file_path.endswith(ext) 
            for ext in ['.yaml', '.yml', '.json']
        ) and any(
            watched_path in file_path 
            for watched_path in self.hot_reloader.watched_paths
        )
        
    def _trigger_reload(self, file_path: str):
        """Dispara o reload de configuração."""
        try:
            asyncio.create_task(
                self.hot_reloader.reload_config(file_path)
            )
        except Exception as e:
            logger.error(f"Erro ao disparar reload para {file_path}: {e}")

class ConfigurationHotReloader:
    """
    Sistema de hot-reload para configurações QUALIA.
    
    Funcionalidades:
    - File watchers para detectar mudanças
    - Validação de configurações antes de aplicar
    - Notificação de componentes sobre mudanças
    - Rollback automático em caso de erro
    - Versionamento de configurações
    """
    
    def __init__(self, 
                 watched_paths: List[str] = None,
                 backup_dir: str = "config/backups",
                 max_versions: int = 10):
        self.watched_paths = watched_paths or [
            "config/bayesian_optimization.yaml",
            "config/holographic_trading_config.yaml",
            "config/strategy_parameters.yaml",
            "config/fwh_scalp_config.yaml"  # YAA-FIX: Usar YAML unificado
        ]
        
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_versions = max_versions
        self.versions: Dict[str, List[ConfigVersion]] = {}
        self.reload_history: List[ReloadEvent] = []
        
        # Callbacks para notificar componentes
        self.reload_callbacks: Dict[str, List[Callable]] = {}
        
        # File watcher
        self.observer: Optional[Observer] = None
        self.file_handler = ConfigFileHandler(self)
        
        # Estado
        self.is_running = False
        self.reload_lock = asyncio.Lock()
        
        logger.info("🔄 ConfigurationHotReloader inicializado")
        logger.info(f"📁 Monitorando: {self.watched_paths}")
        logger.info(f"💾 Backups em: {self.backup_dir}")
        
    def start(self):
        """Inicia o sistema de hot-reload."""
        if self.is_running:
            logger.warning("Hot-reloader já está rodando")
            return
            
        try:
            # Criar versões iniciais dos arquivos monitorados
            self._create_initial_versions()
            
            # Iniciar file watcher
            self.observer = Observer()
            
            # Adicionar watchers para cada diretório
            watched_dirs = set()
            for path in self.watched_paths:
                dir_path = str(Path(path).parent)
                if dir_path not in watched_dirs:
                    self.observer.schedule(
                        self.file_handler,
                        dir_path,
                        recursive=False
                    )
                    watched_dirs.add(dir_path)
                    
            self.observer.start()
            self.is_running = True
            
            logger.info("✅ Hot-reload system iniciado com sucesso")
            
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar hot-reload system: {e}")
            raise
            
    def stop(self):
        """Para o sistema de hot-reload."""
        if not self.is_running:
            return
            
        try:
            if self.observer:
                self.observer.stop()
                self.observer.join()
                
            self.is_running = False
            logger.info("🛑 Hot-reload system parado")
            
        except Exception as e:
            logger.error(f"❌ Erro ao parar hot-reload system: {e}")
            
    def register_callback(self, config_path: str, callback: Callable):
        """Registra callback para ser chamado quando configuração mudar."""
        if config_path not in self.reload_callbacks:
            self.reload_callbacks[config_path] = []
            
        self.reload_callbacks[config_path].append(callback)
        logger.debug(f"📞 Callback registrado para {config_path}")
        
    def unregister_callback(self, config_path: str, callback: Callable):
        """Remove callback registrado."""
        if config_path in self.reload_callbacks:
            try:
                self.reload_callbacks[config_path].remove(callback)
                logger.debug(f"📞 Callback removido para {config_path}")
            except ValueError:
                pass
                
    async def reload_config(self, config_path: str) -> ReloadEvent:
        """
        Recarrega uma configuração específica.
        
        Args:
            config_path: Caminho para o arquivo de configuração
            
        Returns:
            ReloadEvent: Evento com resultado do reload
        """
        async with self.reload_lock:
            return await self._perform_reload(config_path)
            
    async def _perform_reload(self, config_path: str) -> ReloadEvent:
        """Executa o reload de configuração com validação e rollback."""
        start_time = datetime.now()
        
        try:
            logger.info(f"🔄 Iniciando reload de {config_path}")
            
            # Obter versão atual
            current_version = self._get_current_version(config_path)
            
            # Carregar nova configuração
            new_config = await self._load_config_file(config_path)
            
            # Validar nova configuração
            validation_result = await self._validate_config(config_path, new_config)
            if not validation_result.is_valid:
                # Falha na validação
                event = ReloadEvent(
                    timestamp=start_time,
                    config_path=config_path,
                    status=ReloadStatus.VALIDATION_ERROR,
                    version_before=current_version.version_id if current_version else "none",
                    version_after=None,
                    error_message=validation_result.error_message
                )
                self.reload_history.append(event)
                logger.error(f"❌ Validação falhou para {config_path}: {validation_result.error_message}")
                return event
                
            # Criar nova versão
            new_version = self._create_version(config_path, new_config)
            
            # Notificar componentes
            await self._notify_components(config_path, new_config)
            
            # Sucesso
            event = ReloadEvent(
                timestamp=start_time,
                config_path=config_path,
                status=ReloadStatus.SUCCESS,
                version_before=current_version.version_id if current_version else "none",
                version_after=new_version.version_id
            )
            self.reload_history.append(event)
            
            logger.info(f"✅ Reload bem-sucedido para {config_path}")
            logger.info(f"📊 Versão: {current_version.version_id if current_version else 'none'} → {new_version.version_id}")
            
            return event
            
        except Exception as e:
            # Erro durante reload - tentar rollback
            logger.error(f"❌ Erro durante reload de {config_path}: {e}")
            
            rollback_event = await self._attempt_rollback(config_path, current_version)
            
            event = ReloadEvent(
                timestamp=start_time,
                config_path=config_path,
                status=ReloadStatus.FAILED,
                version_before=current_version.version_id if current_version else "none",
                version_after=None,
                error_message=str(e),
                rollback_version=rollback_event.version_after if rollback_event else None
            )
            self.reload_history.append(event)
            
            return event

    def _create_initial_versions(self):
        """Cria versões iniciais dos arquivos monitorados."""
        for config_path in self.watched_paths:
            try:
                if Path(config_path).exists():
                    with open(config_path, 'r', encoding='utf-8') as f:
                        if config_path.endswith(('.yaml', '.yml')):
                            config_data = yaml.safe_load(f) or {}
                        else:
                            config_data = json.load(f)

                    version = self._create_version(config_path, config_data, "Initial version")
                    logger.debug(f"📋 Versão inicial criada para {config_path}: {version.version_id}")

            except Exception as e:
                logger.warning(f"⚠️ Não foi possível criar versão inicial para {config_path}: {e}")

    def _get_current_version(self, config_path: str) -> Optional[ConfigVersion]:
        """Obtém a versão atual de uma configuração."""
        versions = self.versions.get(config_path, [])
        return versions[-1] if versions else None

    async def _load_config_file(self, config_path: str) -> Dict[str, Any]:
        """Carrega arquivo de configuração."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith(('.yaml', '.yml')):
                    return yaml.safe_load(f) or {}
                else:
                    return json.load(f)
        except Exception as e:
            raise ValueError(f"Erro ao carregar {config_path}: {e}")

    async def _validate_config(self, config_path: str, config_data: Dict[str, Any]) -> 'ValidationResult':
        """Valida configuração antes de aplicar."""
        try:
            # Validações específicas por tipo de arquivo
            if "bayesian_optimization" in config_path:
                return await self._validate_bayesian_config(config_data)
            elif "holographic_trading" in config_path:
                return await self._validate_trading_config(config_data)
            elif "strategy_parameters" in config_path:
                return await self._validate_strategy_config(config_data)
            else:
                # Validação genérica
                return ValidationResult(True, "Configuração válida")

        except Exception as e:
            return ValidationResult(False, f"Erro na validação: {e}")

    async def _validate_bayesian_config(self, config: Dict[str, Any]) -> 'ValidationResult':
        """Valida configuração do Bayesian Optimizer."""
        required_fields = ['optimization', 'pruning', 'multi_fidelity']

        for field in required_fields:
            if field not in config:
                return ValidationResult(False, f"Campo obrigatório ausente: {field}")

        # Validar ranges de parâmetros
        opt_config = config.get('optimization', {})
        if 'parameter_ranges' in opt_config:
            ranges = opt_config['parameter_ranges']

            # Validar price_amplification
            if 'price_amplification' in ranges:
                price_range = ranges['price_amplification']
                if not (0.1 <= price_range.get('min', 0) <= price_range.get('max', 0) <= 20.0):
                    return ValidationResult(False, "Range de price_amplification inválido")

            # Validar news_amplification
            if 'news_amplification' in ranges:
                news_range = ranges['news_amplification']
                if not (0.1 <= news_range.get('min', 0) <= news_range.get('max', 0) <= 30.0):
                    return ValidationResult(False, "Range de news_amplification inválido")

            # Validar min_confidence
            if 'min_confidence' in ranges:
                conf_range = ranges['min_confidence']
                if not (0.1 <= conf_range.get('min', 0) <= conf_range.get('max', 0) <= 1.0):
                    return ValidationResult(False, "Range de min_confidence inválido")

        return ValidationResult(True, "Configuração Bayesian válida")

    async def _validate_trading_config(self, config: Dict[str, Any]) -> 'ValidationResult':
        """Valida configuração de trading."""
        # Validar configurações críticas de trading
        if 'amplification' in config:
            amp_config = config['amplification']

            # Validar limites de amplificação
            if 'limits' in amp_config:
                limits = amp_config['limits']
                max_price = limits.get('max_price_amplification', 0)
                max_news = limits.get('max_news_amplification', 0)

                if max_price <= 0 or max_price > 50:
                    return ValidationResult(False, "Limite de price_amplification inválido")
                if max_news <= 0 or max_news > 100:
                    return ValidationResult(False, "Limite de news_amplification inválido")

        return ValidationResult(True, "Configuração de trading válida")

    async def _validate_strategy_config(self, config: Dict[str, Any]) -> 'ValidationResult':
        """Valida configuração de estratégia."""
        # Validações básicas de estratégia
        if not isinstance(config, dict):
            return ValidationResult(False, "Configuração deve ser um dicionário")

        return ValidationResult(True, "Configuração de estratégia válida")

    def _create_version(self, config_path: str, config_data: Dict[str, Any], description: str = "") -> ConfigVersion:
        """Cria nova versão de configuração."""
        timestamp = datetime.now()
        version_id = f"{timestamp.strftime('%Y%m%d_%H%M%S')}_{hash(str(config_data)) % 10000:04d}"

        # Calcular checksum
        config_str = json.dumps(config_data, sort_keys=True)
        checksum = hashlib.md5(config_str.encode()).hexdigest()

        # Criar backup
        backup_filename = f"{Path(config_path).stem}_{version_id}.backup"
        backup_path = self.backup_dir / backup_filename

        try:
            with open(backup_path, 'w', encoding='utf-8') as f:
                if config_path.endswith(('.yaml', '.yml')):
                    yaml.dump(config_data, f, default_flow_style=False)
                else:
                    json.dump(config_data, f, indent=2)
        except Exception as e:
            logger.warning(f"⚠️ Erro ao criar backup {backup_path}: {e}")
            backup_path = ""

        # Criar versão
        version = ConfigVersion(
            version_id=version_id,
            timestamp=timestamp,
            config_path=config_path,
            config_data=config_data.copy(),
            backup_path=str(backup_path),
            checksum=checksum,
            description=description or f"Auto-backup {timestamp.strftime('%Y-%m-%d %H:%M:%S')}"
        )

        # Adicionar à lista de versões
        if config_path not in self.versions:
            self.versions[config_path] = []

        self.versions[config_path].append(version)

        # Limitar número de versões
        if len(self.versions[config_path]) > self.max_versions:
            old_version = self.versions[config_path].pop(0)
            # Remover backup antigo
            try:
                if old_version.backup_path and Path(old_version.backup_path).exists():
                    Path(old_version.backup_path).unlink()
            except Exception as e:
                logger.warning(f"⚠️ Erro ao remover backup antigo {old_version.backup_path}: {e}")

        return version

    async def _notify_components(self, config_path: str, config_data: Dict[str, Any]):
        """Notifica componentes sobre mudança de configuração."""
        callbacks = self.reload_callbacks.get(config_path, [])

        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(config_path, config_data)
                else:
                    callback(config_path, config_data)

            except Exception as e:
                logger.error(f"❌ Erro ao executar callback para {config_path}: {e}")

    async def _attempt_rollback(self, config_path: str, target_version: Optional[ConfigVersion]) -> Optional[ReloadEvent]:
        """Tenta fazer rollback para versão anterior."""
        if not target_version:
            logger.warning(f"⚠️ Nenhuma versão disponível para rollback de {config_path}")
            return None

        try:
            logger.info(f"🔄 Tentando rollback de {config_path} para versão {target_version.version_id}")

            # Restaurar configuração da versão anterior
            with open(config_path, 'w', encoding='utf-8') as f:
                if config_path.endswith(('.yaml', '.yml')):
                    yaml.dump(target_version.config_data, f, default_flow_style=False)
                else:
                    json.dump(target_version.config_data, f, indent=2)

            # Notificar componentes sobre rollback
            await self._notify_components(config_path, target_version.config_data)

            event = ReloadEvent(
                timestamp=datetime.now(),
                config_path=config_path,
                status=ReloadStatus.ROLLBACK,
                version_before="failed",
                version_after=target_version.version_id
            )

            logger.info(f"✅ Rollback bem-sucedido para {config_path}")
            return event

        except Exception as e:
            logger.error(f"❌ Falha no rollback de {config_path}: {e}")
            return None

    def get_version_history(self, config_path: str) -> List[ConfigVersion]:
        """Obtém histórico de versões de uma configuração."""
        return self.versions.get(config_path, []).copy()

    def get_reload_history(self, limit: int = 50) -> List[ReloadEvent]:
        """Obtém histórico de reloads."""
        return self.reload_history[-limit:] if limit else self.reload_history.copy()

    async def manual_rollback(self, config_path: str, version_id: str) -> ReloadEvent:
        """Executa rollback manual para versão específica."""
        async with self.reload_lock:
            versions = self.versions.get(config_path, [])
            target_version = next((v for v in versions if v.version_id == version_id), None)

            if not target_version:
                event = ReloadEvent(
                    timestamp=datetime.now(),
                    config_path=config_path,
                    status=ReloadStatus.FAILED,
                    version_before="unknown",
                    version_after=None,
                    error_message=f"Versão {version_id} não encontrada"
                )
                self.reload_history.append(event)
                return event

            rollback_event = await self._attempt_rollback(config_path, target_version)
            if rollback_event:
                self.reload_history.append(rollback_event)

            return rollback_event or ReloadEvent(
                timestamp=datetime.now(),
                config_path=config_path,
                status=ReloadStatus.FAILED,
                version_before="unknown",
                version_after=None,
                error_message="Rollback falhou"
            )

@dataclass
class ValidationResult:
    """Resultado da validação de configuração."""
    is_valid: bool
    error_message: str = ""

# Instância global do hot-reloader
_global_hot_reloader: Optional[ConfigurationHotReloader] = None

def get_global_hot_reloader() -> ConfigurationHotReloader:
    """Obtém instância global do hot-reloader."""
    global _global_hot_reloader
    if _global_hot_reloader is None:
        _global_hot_reloader = ConfigurationHotReloader()
    return _global_hot_reloader

def start_hot_reload_system():
    """Inicia o sistema global de hot-reload."""
    reloader = get_global_hot_reloader()
    reloader.start()
    return reloader

def stop_hot_reload_system():
    """Para o sistema global de hot-reload."""
    global _global_hot_reloader
    if _global_hot_reloader:
        _global_hot_reloader.stop()
        _global_hot_reloader = None
