#!/usr/bin/env python3
"""QUALIA Trading System.

Sistema completo de trading com consciência quântica e análise holográfica.
Integra diversos módulos, incluindo:
    - Consciência QUALIA unificada
    - Sistema holográfico de análise de mercado
    - Engine de decisão QAST Oracle
    - Interface de execução robusta
    - Controles de segurança avançados
    - Serviço de memória quântica (QuantumPatternMemory + HolographicMemory)
    - Monitoramento em tempo real
"""

import asyncio
import sys
import os
import time
import json
import signal
import yaml
from datetime import datetime, timezone, timedelta
from typing import Any, Dict, List, Optional
from pathlib import Path
import argparse
import logging
import collections.abc
from collections import deque, defaultdict

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

# Load environment variables
try:
    from dotenv import load_dotenv

    load_dotenv()
except ImportError:
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                if line.strip() and not line.startswith("#"):
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value

from qualia.core.unified_qualia_consciousness import (  # noqa: E402
    UnifiedQUALIAConsciousness,
)  # noqa: E402
from qualia.core.qast_oracle_decision_engine import (  # noqa: E402
    QASTOracleDecisionEngine,
)  # noqa: E402
from qualia.core.qualia_execution_interface import (  # noqa: E402
    QUALIAExecutionInterface,
)  # noqa: E402
from qualia.consciousness.holographic_universe import (  # noqa: E402
    HolographicMarketUniverse,
)  # noqa: E402
from qualia.consciousness.real_data_collectors import (  # noqa: E402
    RealDataCollector,
)
from qualia.consciousness.enhanced_data_collector import (  # noqa: E402
    EnhancedDataCollector,
)  # noqa: E402
from qualia.consciousness.holographic_trading_adapter import (  # noqa: E402
    HolographicTradingAdapter,
    HolographicSignal,
)
from qualia.risk_management.advanced_risk_manager import (  # noqa: E402
    AdvancedRiskManager,
)  # noqa: E402
from qualia.exchanges.kucoin_client import KuCoinClient  # noqa: E402
from qualia.config.config_loader import load_env_and_json  # noqa: E402
from qualia.utils.logger import get_logger, setup_logging  # noqa: E402
from qualia.market.base_integration import CryptoDataFetcher  # noqa: E402
from qualia.core.data_warmup import DataWarmupManager  # noqa: E402
from qualia.utils.cache import CacheManager, get_cache_manager  # noqa: E402

try:
    from qualia.market.symbol_utils import to_canonical_format  # noqa: E402
except Exception:  # pragma: no cover - fallback for test stubs
    to_canonical_format = lambda symbol: symbol
from qualia.memory.service import MemoryService  # noqa: E402
from qualia.memory.holographic_memory import HolographicMemory  # noqa: E402
from qualia.memory.quantum_pattern_memory import QuantumPatternMemory  # noqa: E402
from qualia.config.settings import settings  # noqa: E402
from qualia.core.qast_oracle_decision_engine import OracleDecision  # noqa: E402
from qualia.event_bus import (  # noqa: E402
    MarketDataUpdated,
    TradingSignalGenerated,
    AsyncEventBus,
)
from qualia.events import *  # noqa: E402,F401,F403
from qualia.orchestration import loops  # noqa: E402
from qualia.metacognition.metacognition_trading import (  # noqa: E402
    QUALIAMetacognitionTrading,
)
from qualia.adaptive_evolution import AdaptiveConsciousnessEvolution  # noqa: E402
from qualia.utils.event_bus import (  # noqa: E402
    OracleDecisionsUpdated,
    HolographicSignalsUpdated,
    SystemShutdown,
)

# YAA: Import ProductionOptimizer for integration with robust path handling
PRODUCTION_OPTIMIZER_AVAILABLE = False
try:
    # Adicionar paths de forma robusta
    root_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(root_dir / "src"))
    from production_optimizer import ProductionOptimizer
    PRODUCTION_OPTIMIZER_AVAILABLE = True
except ImportError:
    try:
        from src.production_optimizer import ProductionOptimizer
        PRODUCTION_OPTIMIZER_AVAILABLE = True
    except ImportError:
        PRODUCTION_OPTIMIZER_AVAILABLE = False
        # Logger será definido mais tarde, então não podemos usar aqui

# YAA: Imports for internal universe simulation
from qualia.core.simulation_qast_core import SimulationQASTCore
from qualia.farsight.holographic_extension import HolographicFarsightEngine
from qualia.consciousness.social_simulation_universe import SocialSimulationUniverse
from qualia.personas.base import BasePersona
from qualia.personas.retail_cluster import RetailCluster
from qualia.personas.momentum_quant import MomentumQuant
import importlib
from qualia.risk_management.advanced_risk_manager import AdvancedRiskManager
from qualia.metacognition.meta_strategy_rl_agent import MetaStrategyRLAgent
from qualia.consciousness.holographic_warmup import HolographicWarmupManager

logger = get_logger(__name__)


def configure_library_loggers(debug: bool) -> None:
    """Adjust log level for third-party libraries."""

    # Configurar nível baseado no flag debug
    level = logging.DEBUG if debug else logging.WARNING

    # Bibliotecas externas que devem ser menos verbosas
    external_libs = [
        "statsd",
        "stevedore",
        "ccxt",  # Exchange library
        "ccxt.base.exchange",  # CCXT exchange logs
        "urllib3",  # HTTP requests
        "asyncio",  # Async operations
        "aiohttp",  # HTTP client
        "websockets",  # WebSocket connections
        "httpx",  # HTTP client alternative
    ]

    for name in external_libs:
        lib_logger = logging.getLogger(name)
        lib_logger.setLevel(level)
        # Suprimir logs de DEBUG completamente para ccxt
        if name.startswith("ccxt") and not debug:
            lib_logger.setLevel(logging.ERROR)  # Só erros críticos


# A QASTOracleDecisionEngine centraliza o núcleo quântico do sistema,
# instanciando `TradingQASTCore` e `QUALIAQuantumUniverse`.


class QUALIATradingSystem:
    """
    QUALIA Trading System
    Arquitetura:
      Data Collection Layer
      Enhanced Data Collector (OHLCV + Quantum Encoders)
      Real Data Collector (Holographic Events)
      News & Sentiment Collectors
      Analysis & Decision Layer
      Holographic Market Universe (Field Simulation)
      QAST Oracle Decision Engine (Central Brain)
      Unified QUALIA Consciousness (Meta-Orchestration)
      Execution & Risk Layer
      Advanced Risk Manager (Multi-layer Protection)
      QUALIA Execution Interface (Position Management)
      KuCoin Client (Exchange Integration)
      Safety & Monitoring Layer
      Real-time Performance Monitoring
      Emergency Stop Systems
      Adaptive Safety Controls
    """

    # YAA-Refactor: Mapear os loops do módulo importado que ainda são necessários
    _data_collection_loop = loops._data_collection_loop
    _monitoring_loop = loops._monitoring_loop
    _safety_monitoring_loop = loops._safety_monitoring_loop
    _consciousness_loop = loops._consciousness_loop
    _metacognition_loop = loops._metacognition_loop
    _execution_cycle_loop = (
        loops._execution_cycle_loop
    )  # Mantido para gerenciar PnL de posições

    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        config_path: str = "",
        hours: float = 0.0,
        mode: str = "paper_trading",
        aggressive_test: bool = False,
        log_level: str = "INFO",
        trace: bool = False,
        force_trade_symbol: Optional[str] = None,
        disable_metacognition: bool = False,
        metacognition_config: Optional[str] = None,
        simulation_core: Optional[SimulationQASTCore] = None,  # Adicionado
    ):
        """Inicializa o sistema de trading QUALIA completo.

        Parameters
        ----------
        metacognition_config : Optional[str]
            Caminho para a configuração específica da camada de metacognição.
        simulation_core : Optional[SimulationQASTCore]
            Instância do motor de simulação da realidade interna.
        """
        self.mode = mode
        self.aggressive_test = aggressive_test
        self.log_level = log_level
        self.start_time = datetime.now(timezone.utc)
        # Este timestamp será redefinido para ``time.time()`` quando
        # os loops principais forem iniciados.
        self.run_duration = timedelta(hours=hours) if hours and hours > 0 else None
        self.config = config
        self.config_path = config_path
        self.trace = trace
        self.force_trade_symbol = (
            to_canonical_format(force_trade_symbol) if force_trade_symbol else None
        )
        self.disable_metacognition = disable_metacognition
        self.metacognition_cfg = metacognition_config
        self.running = False
        self.shutdown_requested = False
        self.emergency_stop = False
        self.data_warmed_up = False
        self.tasks: Dict[str, asyncio.Task] = {}
        self.task_group: Optional[asyncio.TaskGroup] = None

        # YAA TASK-05: Inicializar o atributo trade_history que faltava
        self.trade_history: List[Dict[str, Any]] = []

        # YAA TASK-05: Restaurar a instanciação do EventBus que foi removida
        self.event_bus = AsyncEventBus()
        # Flag to ensure event handlers are registered only once
        self.handlers_registered = False

        # YAA: Integração com ProductionOptimizer
        self.production_optimizer: Optional[ProductionOptimizer] = None
        self.optimization_discoveries_applied = False
        self.use_optimized_config = False

        # Atributos de componentes - serão inicializados em `initialize`
        self.kucoin_client: Optional[KuCoinClient] = None
        self.market_integration: Optional[CryptoDataFetcher] = None
        self.holographic_universe: Optional[HolographicMarketUniverse] = None
        self.consciousness: Optional[UnifiedQUALIAConsciousness] = None
        self.oracle_engine: Optional[QASTOracleDecisionEngine] = None
        self.execution_interface: Optional[QUALIAExecutionInterface] = None
        self.enhanced_data_collector: Optional[EnhancedDataCollector] = None
        self.real_data_collector: Optional[RealDataCollector] = None
        self.holographic_adapter: Optional[HolographicTradingAdapter] = None
        self.risk_manager: Optional[AdvancedRiskManager] = None
        self.memory_service: Optional[MemoryService] = None
        self.adaptive_evolution: Optional[AdaptiveConsciousnessEvolution] = None
        self.metacognition: Optional[QUALIAMetacognitionTrading] = None

        # Instâncias únicas de serviços compartilhados
        self.cache_manager: CacheManager = get_cache_manager(
            cache_dir=settings.cache_dir,
            expiry_seconds=300,
            cache_type="disk",
        )

        exchanges_config = self.config.get("exchanges") or {}
        kucoin_cfg = exchanges_config.get("kucoin", {})
        api_key = os.getenv("KUCOIN_API_KEY", kucoin_cfg.get("api_key", ""))
        api_secret = os.getenv("KUCOIN_SECRET_KEY", kucoin_cfg.get("api_secret", ""))
        passphrase = os.getenv("KUCOIN_PASSPHRASE", kucoin_cfg.get("password", ""))

        # YAA Refactor: Centralizar a criação do cliente da exchange
        if all([api_key, api_secret, passphrase]):
            self.kucoin_client = KuCoinClient(
                {
                    "api_key": api_key,
                    "api_secret": api_secret,
                    "password": passphrase,
                    "sandbox": kucoin_cfg.get("sandbox", True),
                    "exchange_id": "kucoin",
                }
            )
            self.market_integration = self.kucoin_client
        else:
            self.kucoin_client = None
            self.market_integration = None

        # YAA-Refactor: Atributos que foram removidos e são necessários para os loops
        self.quantum_states: Dict[str, Dict[str, Any]] = {}
        self.warmup_manager: Optional[DataWarmupManager] = None
        self.holographic_warmup_manager: Optional[HolographicWarmupManager] = None

        # YAA TASK-05: Inicializar atributos de segurança antes de carregar as configurações
        self.max_daily_loss: float = 0.05
        self.max_drawdown: float = 0.15
        self.max_positions: int = 10

        # Controles de segurança (carregados da configuração)
        self._load_safety_settings()

        # YAA TASK-05: Inicializar atributos de trading antes de carregar as configurações
        self.base_currency: str = "USDT"
        self.quote_currencies: List[str] = []

        # YAA TASK-05: Restaurar atributos de rastreamento de performance
        self.performance_data: List[Dict[str, Any]] = []
        self.holographic_metrics: List[Dict[str, Any]] = []
        self.decision_metrics: List[Dict[str, Any]] = []
        self._state_lock = asyncio.Lock()

        # Configuração de trading
        trading_config = config.get("trading", {})

        # Configuração de tempo
        timing_config = config.get("timing", {})

        # Timing Configuration
        default_timing = {
            "data_collection_interval": 30.0,
            "holographic_evolution_interval": 15.0,
            "decision_cycle_interval": 60.0,
            "execution_cycle_interval": 5.0,
            "monitoring_interval": 10.0,
            "safety_check_interval": 5.0,
            "metacognition_interval": 30.0,
            "metacognition_idle_info_interval": 10,  # deve ser > 0
        }
        self.timing = {
            **default_timing,
            **self.config.get("timing", {}),
        }

        # Disponibiliza referências aos loops principais para facilitar testes
        self._data_collection_loop = loops._data_collection_loop
        self._holographic_evolution_loop = loops._holographic_evolution_loop
        self._decision_cycle_loop = loops._decision_cycle_loop
        self._consciousness_loop = loops._consciousness_loop
        self._execution_cycle_loop = loops._execution_cycle_loop
        self._monitoring_loop = loops._monitoring_loop
        self._safety_monitoring_loop = loops._safety_monitoring_loop
        self._metacognition_loop = loops._metacognition_loop

        logger.info(f"QUALIA Trading System inicializado (modo: {mode})")

        # YAA: Detectar e carregar configuração otimizada automaticamente
        self._detect_and_load_optimized_config()

        # YAA-TASK-003: Histórico de volatilidade para calibração dinâmica
        vol_history_window = self.config.get("signal_approval", {}).get(
            "volatility_history_window", 100
        )
        self._volatility_history = defaultdict(lambda: deque(maxlen=vol_history_window))

        self._last_status_log_time = 0
        self._shutdown_event = asyncio.Event()

        # Histórico de confiança para avaliação de risco dinâmica (a ser substituído)
        self._confidence_history = deque(maxlen=50)
        self._threshold_decay_factor = 0.95

        # Fila central para comunicação entre loops
        self.data_queue = asyncio.Queue(maxsize=100)

        # Contadores de métricas sem dados reais
        self.zero_metric_counts: Dict[str, int] = defaultdict(int)

    def _detect_and_load_optimized_config(self) -> None:
        """
        YAA-FIX: Configuração unificada - usar apenas YAML.

        O arquivo production_config.json foi consolidado na seção 'production_optimization'
        do arquivo YAML principal para eliminar duplicação e conflitos.
        """

        # YAA-FIX: Verificar se temos seção production_optimization no YAML
        if self.config and 'production_optimization' in self.config:
            logger.info("✅ Configuração de produção encontrada na seção 'production_optimization' do YAML")
            self.use_optimized_config = True

            # Log das configurações encontradas
            prod_config = self.config['production_optimization']
            if 'optimization' in prod_config:
                opt_config = prod_config['optimization']
                logger.info(f"   Study: {opt_config.get('study_name', 'N/A')}")
                logger.info(f"   Trials por ciclo: {opt_config.get('n_trials_per_cycle', 'N/A')}")

            if 'base_params' in prod_config:
                base_params = prod_config['base_params']
                logger.info(f"   Parâmetros base: {len(base_params)} configurados")

        else:
            logger.info("📋 Usando configuração padrão (seção production_optimization não encontrada)")
            self.use_optimized_config = False

        # YAA-FIX: Garantir que self.config não seja None
        if self.config is None:
            logger.warning("⚠️ Configuração é None - carregando configuração padrão")
            try:
                self.config = load_env_and_json()
            except Exception as e:
                logger.error(f"❌ Erro ao carregar configuração padrão: {e}")
                self.config = {}

        # YAA-DEPRECATION-WARNING: Avisar se production_config.json ainda existe
        production_config_path = Path("config/production_config.json")
        if production_config_path.exists():
            logger.warning("⚠️ DEPRECATED: config/production_config.json detectado mas IGNORADO")
            logger.warning("   Use a seção 'production_optimization' no arquivo YAML principal")
            logger.warning("   O arquivo JSON será removido em versões futuras")

    def _apply_optimization_discoveries(self) -> None:
        """Aplica descobertas de otimização se configuração otimizada estiver ativa."""

        if not self.use_optimized_config or self.optimization_discoveries_applied:
            return

        logger.info("🧬 Aplicando descobertas de otimização no sistema...")

        # As descobertas já estão na configuração, mas garantimos que sejam aplicadas
        base_params = self.config.get("base_params", {})

        if base_params.get("news_amplification") == 11.3:
            logger.info("   ✅ news_amplification: 11.3 (fator mais impactante)")
        if base_params.get("price_amplification") == 1.0:
            logger.info("   ✅ price_amplification: 1.0 (otimizado)")
        if base_params.get("min_confidence") == 0.37:
            logger.info("   ✅ min_confidence: 0.37 (ajustado)")

        self.optimization_discoveries_applied = True
        logger.info("🏆 Descobertas aplicadas com sucesso!")

    def _setup_signal_handlers(self):
        """Configura handlers para shutdown gracioso."""
        self._shutdown_count = 0  # YAA: Contador de sinais recebidos

        def signal_handler(signum, frame):
            self._shutdown_count += 1

            if self._shutdown_count == 1:
                logger.info(
                    f"📡 Sinal {signum} recebido, iniciando shutdown gracioso..."
                )
                self.shutdown_requested = True
                self.running = False  # YAA: Força parada imediata dos loops
            elif self._shutdown_count == 2:
                logger.warning(
                    " Segundo sinal recebido! Forçando cancelamento de tasks..."
                )
                # YAA: Força cancelamento de todas as tasks após 2 sinais
                if hasattr(self, "task_group") and self.task_group:
                    try:
                        # TaskGroup não expõe diretamente as tasks, então precisamos
                        # forçar o cancelamento de forma diferente
                        for task in asyncio.all_tasks():
                            if not task.done() and task != asyncio.current_task():
                                task.cancel()
                    except Exception as e:
                        logger.error(
                            f"Erro ao cancelar tasks: {e}",
                            exc_info=True,
                        )
            else:
                logger.error("🚨  Múltiplos sinais recebidos! Terminando processo...")
                import sys

                sys.exit(1)  # YAA: Força saída após 3+ sinais

        if hasattr(signal, "SIGTERM"):
            signal.signal(signal.SIGTERM, signal_handler)
        if hasattr(signal, "SIGINT"):
            signal.signal(signal.SIGINT, signal_handler)

    def _load_safety_settings(self) -> None:
        """Carrega controles de segurança da configuração."""

        safety_cfg = (
            self.config.get("risk_management", {}).get("safety", {})
            if self.config
            else {}
        )

        self.max_daily_loss = float(
            safety_cfg.get("max_daily_loss_pct", self.max_daily_loss)
        )
        self.max_drawdown = float(safety_cfg.get("max_drawdown_pct", self.max_drawdown))
        self.max_positions = int(
            safety_cfg.get("max_open_positions", self.max_positions)
        )

    async def initialize(self) -> bool:
        """Initialize the trading system and set up event handlers.

        Returns
        -------
        bool
            ``True`` if the initialization succeeded, ``False`` otherwise.
        """
        logger.info("\n" + "=" * 80)
        logger.info("🌌  QUALIA Trading System - Arquitetura Reativa a Eventos")
        logger.info("=" * 80)
        logger.info(f"🕒  Início: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"⚙️  Modo: {self.mode.upper()}")
        if self.mode == "live":
            logger.info("⚠️   ATENÇÃO: MODO LIVE - DINHEIRO REAL!")
        logger.info("=" * 80)

        try:
            # YAA: Inicializar ProductionOptimizer se disponível e configuração otimizada ativa
            if PRODUCTION_OPTIMIZER_AVAILABLE and self.use_optimized_config:
                logger.info("🔧 Inicializando ProductionOptimizer integrado...")
                self.production_optimizer = ProductionOptimizer()
                await self.production_optimizer.initialize()
                logger.info("✅ ProductionOptimizer integrado com sucesso")

            # YAA: Aplicar descobertas de otimização
            self._apply_optimization_discoveries()

            logger.info("✅  Configuração já carregada e mesclada.")

            trading_cfg = self.config.get("trading", {})
            self.base_currency = trading_cfg.get("base_currency", self.base_currency)
            self.quote_currencies = trading_cfg.get(
                "quote_currencies", self.quote_currencies
            )
            logger.info(f"💱  Base currency: {self.base_currency}")
            if self.quote_currencies:
                logger.info(f"💱  Quote currencies: {', '.join(self.quote_currencies)}")
            else:
                logger.warning("Nenhuma quote currency configurada")

            # Carregar parâmetros de segurança
            self._load_safety_settings()

            # Extrair parâmetros principais para a instância
            raw_symbols = self.config["system"]["symbols"]
            self.symbols = [to_canonical_format(s) for s in raw_symbols]
            self.timeframes = self.config["system"]["timeframes"]
            capital = self.config["system"]["capital"]

            # Atualizar modo na configuração
            self.config["system"]["mode"] = self.mode

            logger.info(f"💰  Capital: ${capital:,.2f}")
            logger.info(f"📊  Símbolos: {', '.join(self.symbols)}")
            logger.info(f"  Timeframes: {', '.join(self.timeframes)}")

            # Mostrar controles de segurança
            if self.mode == "live":
                logger.info("🛡¸  Controles de Segurança:")
                logger.info(f"   📉  Perda máxima diária: {self.max_daily_loss:.1%}")
                logger.info(f"   📊  Drawdown máximo: {self.max_drawdown:.1%}")
                logger.info(f"   📈  Posições máximas: {self.max_positions}")

            # Inicializar Data Collection Layer
            logger.info("📡  Inicializando Data Collection Layer...")
            await self._initialize_data_collection_layer(self.symbols, self.timeframes)

            # Inicializar Execution & Risk Layer (necessário para KuCoin client e para o warmup)
            logger.info("ðŏޝ  Inicializando Execution & Risk Layer...")
            await self._initialize_execution_risk_layer(capital)

            # YAA TASK-01: O warm-up agora é centralizado no Oracle Engine
            # Não precisamos mais pré-inicializar estratégias aqui
            logger.info(
                "ðŸ¥  Warm-up será executado durante inicialização do Analysis & Decision Layer..."
            )

            logger.info("🧠  Inicializando Analysis & Decision Layer...")
            await self._initialize_analysis_decision_layer(
                self.symbols, self.timeframes, capital
            )
            logger.info("✅ Analysis & Decision Layer inicializada")

            # NOVO: Executar Warm-up Holográfico
            if self.config.get("holographic", {}).get("enable_warmup", True):
                if self.enhanced_data_collector and self.real_data_collector:
                    logger.info("🔥 Executando Warm-up Holográfico...")
                    self.holographic_warmup_manager = HolographicWarmupManager(
                        self.holographic_universe,
                        enhanced_data_collector=self.enhanced_data_collector,
                        real_data_collector=self.real_data_collector,
                    )
                    warmup_success = (
                        await self.holographic_warmup_manager.perform_warmup(
                            warmup_period_hours=self.config.get("holographic", {}).get(
                                "warmup_hours", 72
                            )
                        )
                    )
                    if warmup_success:
                        logger.info("✅ Warm-up Holográfico concluído com sucesso.")
                    else:
                        logger.error(
                            "❌ Falha no Warm-up Holográfico. Abortando inicialização."
                        )
                        return False
                else:
                    logger.warning(
                        "Coletores de dados ausentes. Warm-up Holográfico ignorado."
                    )
            else:
                logger.info("ℹ¸ Warm-up Holográfico desabilitado na configuração.")

            # YAA-Refactor: Subscreve os handlers aos eventos do barramento
            if not self.handlers_registered:
                logger.info("🚌  Registrando Handlers de Eventos...")
                self._register_event_handlers()
                logger.info("✅  Handlers de Eventos registrados.")
            else:
                logger.debug("Event handlers já registrados; ignorando nova chamada.")

            # YAA TASK-01: O warm-up agora é centralizado e executado pelo Oracle Engine
            # A verificação de integridade é feita durante a inicialização do Oracle
            logger.info("✅  Warm-up de dados será validado pelo Oracle Engine.")

            # Configurar Signal Handlers
            logger.info("ðŸ§  Configurando signal handlers...")
            self._setup_signal_handlers()
            logger.info("✅  Signal handlers configurados")

            # Configurar logs específicos com níveis apropriados
            # Reduzir verbosidade para operação normal
            holographic_logger = logging.getLogger(
                "qualia.consciousness.holographic_universe"
            )
            holographic_logger.setLevel(logging.INFO)  # INFO em vez de DEBUG

            # RSS e data collectors - manter apenas INFO
            rss_logger = logging.getLogger("qualia.consciousness.real_data_collectors")
            rss_logger.setLevel(logging.INFO)  # INFO em vez de DEBUG

            # TSVF - logging moderado
            tsvf_logger = logging.getLogger(
                "qualia.strategies.nova_estrategia_qualia.tsvf"
            )
            tsvf_logger.setLevel(logging.INFO)  # INFO em vez de DEBUG

            # Estratégia core - logging moderado
            strategy_logger = logging.getLogger(
                "qualia.strategies.nova_estrategia_qualia.core"
            )
            strategy_logger.setLevel(logging.INFO)  # INFO em vez de DEBUG

            # Adiciona handler específico se não existir
            for logger_obj in [
                holographic_logger,
                rss_logger,
                tsvf_logger,
                strategy_logger,
            ]:
                if not logger_obj.handlers:
                    console_handler = logging.StreamHandler()
                    console_handler.setLevel(logging.INFO)  # INFO em vez de DEBUG
                    formatter = logging.Formatter(
                        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                    )
                    console_handler.setFormatter(formatter)
                    logger_obj.addHandler(console_handler)

            # Aplicar configurações de logging_overrides da configuração
            logging_overrides = self.config.get("logging_overrides", {})
            if logging_overrides:
                logger.info("🛠️  Aplicando configurações de logging personalizadas...")
                for logger_name, level_str in logging_overrides.items():
                    try:
                        level = getattr(logging, level_str.upper())
                        logging.getLogger(logger_name).setLevel(level)
                        logger.debug(f"   {logger_name}: {level_str}")
                    except AttributeError:
                        logger.warning(
                            f"Nível de log inválido '{level_str}' para {logger_name}"
                        )

            logger.info(
                "ðŸ  Logging configurado com níveis apropriados (INFO) para componentes principais"
            )

            logger.info("✅  Sistema inicializado com arquitetura completa!")
            return True

        except Exception as e:
            logger.error(f"❌  Erro na inicialização: {e}", exc_info=True)
            return False

    def _register_event_handlers(self) -> None:
        """Registra os métodos da classe como handlers para eventos do sistema.

        A flag ``handlers_registered`` previne que as inscrições sejam duplicadas
        e a função retorna imediatamente caso os handlers já estejam
        registrados.
        """
        if self.handlers_registered:
            return

        self.event_bus.subscribe(
            "market.data.updated", self._handle_market_data_for_analysis
        )
        self.event_bus.subscribe(
            "holographic.signals.updated", self._handle_holographic_signals
        )
        self.event_bus.subscribe(
            "oracle.decisions.updated", self._handle_oracle_decisions
        )

        # YAA-Refactor: Assina o handler de execução ao evento de sinal final.
        self.event_bus.subscribe(
            "trading.signal.generated", self._execute_trade_handler
        )

        self.handlers_registered = True

    async def _handle_market_data_for_analysis(self, event: MarketDataUpdated) -> None:
        """Recebe dados de mercado e dispara os ciclos de análise e decisão."""

        # YAA-FIX: Log de confirmação para validar o fluxo de eventos
        if event.market_data:
            logger.info(
                f"✅ EVENTO RECEBIDO: MarketDataUpdated para {event.market_data[0].symbol} "
                f"(preço: {event.market_data[0].price}, mudança: {event.market_data[0].change_24h:.2f}%). "
                "Iniciando ciclo de análise..."
            )
        else:
            logger.info(
                "✅ EVENTO RECEBIDO: MarketDataUpdated (sem dados de mercado). Iniciando ciclo de análise..."
            )

        # Executa a evolução holográfica primeiro para gerar/enriquecer os dados
        await self._run_holographic_evolution(event)

        # Após a evolução, o Oracle já tem acesso aos dados enriquecidos
        await self._run_oracle_decision_cycle(event)

    async def _run_holographic_evolution(self, event: MarketDataUpdated) -> None:
        """Lógica extraída do _holographic_evolution_loop para ser um handler."""
        try:
            if (
                not self.holographic_universe
                or not self.holographic_adapter
                or not self.enhanced_data_collector
            ):
                return

            # YAA FIX: Converter dados brutos de MarketDataPoint para dict e enriquecê-los
            raw_data_dicts = [point.to_dict() for point in event.market_data]
            enhanced_data = await self.enhanced_data_collector.process_raw_data(
                raw_data_dicts
            )

            # 1. Injetar eventos de dados no universo holográfico
            h_events = self.enhanced_data_collector.convert_to_holographic_events(
                enhanced_data, [], self.holographic_universe.field_size
            )
            for h_event in h_events:
                await self.holographic_universe.inject_holographic_event(h_event)

            # 2. Evoluir o campo usando o tempo atual
            await self.holographic_universe.step_evolution(time.time())

            # 3. Analisar padrões e publicar resultados
            patterns = self.holographic_universe.analyze_holographic_patterns()
            if patterns:
                # ETAPA 1: Converter padrões brutos em sinais de trading acionáveis
                trading_signals = self.holographic_universe.generate_trading_signals(
                    patterns
                )

                if trading_signals:
                    # ETAPA 2: Adaptar do formato TradingSignal (universo) para HolographicSignal (adaptador)
                    holographic_signals_for_adapter = [
                        HolographicSignal(
                            symbol=ts.symbol,
                            action=ts.action,
                            confidence=ts.confidence,
                            timeframe=ts.timeframe,
                            timestamp=ts.timestamp,
                            pattern_strength=ts.strength,
                            # Outros campos são opcionais ou podem ser extraídos dos metadados se necessário
                        )
                        for ts in trading_signals
                    ]

                    # ETAPA 3: Converter os sinais adaptados para o formato de decisão do Oracle
                    oracle_decisions = (
                        await self.holographic_adapter.convert_signals_to_decisions(
                            holographic_signals_for_adapter
                        )
                    )

                    if oracle_decisions:
                        signal_event = HolographicSignalsUpdated(
                            signals=oracle_decisions, timestamp=time.time()
                        )
                        self.event_bus.publish(
                            "holographic.signals.updated", signal_event
                        )
                        logger.debug(
                            f"Evento HolographicSignalsUpdated publicado com {len(oracle_decisions)} sinais."
                        )

        except Exception as exc:
            logger.error(f"Erro na evolução holográfica reativa: {exc}", exc_info=True)

    async def _run_oracle_decision_cycle(self, event: MarketDataUpdated) -> None:
        """
        YAA TASK-04: Lógica do ciclo de decisão do Oracle.
        Removida verificação redundante de data_warmed_up - o Oracle gerencia seu próprio warmup.
        """
        try:
            # YAA TASK-04: Verificar apenas se o Oracle existe
            if not self.oracle_engine:
                logger.debug("TASK-04: Oracle Engine não disponível ainda")
                return

            # YAA TASK-04: Log detalhado para monitorar execução
            logger.debug(
                f"ðŸ® TASK-04: Iniciando ciclo de decisão do Oracle com {len(event.market_data)} símbolos"
            )

            # Consultar o Oracle para decisões
            oracle_decisions = await self.oracle_engine.consult_oracle()

            if oracle_decisions:
                logger.info(
                    f"ðŏޝ TASK-04: Oracle gerou {len(oracle_decisions)} decisões!"
                )

                # Publicar evento com as decisões
                decision_event = OracleDecisionsUpdated(
                    decisions=oracle_decisions, timestamp=time.time()
                )
                self.event_bus.publish("oracle.decisions.updated", decision_event)

                logger.debug(
                    f"✅ TASK-04: Evento OracleDecisionsUpdated publicado com {len(oracle_decisions)} decisões."
                )

                # Log detalhado das decisões
                for decision in oracle_decisions:
                    logger.info(
                        f"   📌 TASK-04: {decision.symbol} - {decision.action} "
                        f"(conf: {decision.confidence:.3f}, size: {decision.size:.4f})"
                    )
            else:
                logger.debug("TASK-04: Oracle não gerou decisões neste ciclo")

        except Exception as exc:
            logger.error(f"❌ TASK-04: Erro no ciclo de decisão: {exc}", exc_info=True)

    async def _handle_holographic_signals(
        self, event: HolographicSignalsUpdated
    ) -> None:
        """Handler para processar sinais holográficos."""
        logger.info(f"Handler recebeu {len(event.signals)} sinais holográficos.")
        # Lógica de combinação ou execução viria aqui.
        # Por enquanto, apenas logamos e talvez combinemos com os do oráculo.
        # Esta lógica precisaria de uma implementação de "combinador de sinais".
        pass

    async def _handle_oracle_decisions(self, event: OracleDecisionsUpdated) -> None:
        """
        Handler para processar decisões do oráculo, avaliar risco e PUBLICAR
        sinais de execução validados.
        """
        logger.info(
            f"Handler de Decisão recebeu {len(event.decisions)} decisões do oráculo."
        )

        if not self.execution_interface:
            logger.error(
                "Execution Interface não disponível, impossível processar decisões.",
                exc_info=True,
            )
            return

        validated_decisions: List[OracleDecision] = []
        for decision in event.decisions:
            if decision.source == "Bootstrap":
                continue

            symbol_data = None
            if self.oracle_engine and hasattr(
                self.oracle_engine, "latest_enhanced_data"
            ):
                symbol_data = next(
                    (
                        data
                        for data in self.oracle_engine.latest_enhanced_data
                        if data.symbol == decision.symbol
                    ),
                    None,
                )

            risk_assessment = self._enhanced_risk_assessment(
                decision, symbol_data=symbol_data
            )

            if risk_assessment.get("approved"):
                logger.info(
                    f"✅  DECISÃO APROVADA (Risco): {decision.symbol} {decision.action} | Conf: {decision.confidence:.2f}"
                )
                validated_decisions.append(decision)
            else:
                logger.warning(
                    f"🚫  DECISÃO REJEITADA (Risco): {decision.symbol} {decision.action} - Razão: {risk_assessment.get('reason')}"
                )

        if validated_decisions:
            # Publica um único evento com a lista de decisões validadas
            signal_event = TradingSignalGenerated(decisions=validated_decisions)
            self.event_bus.publish("trading.signal.generated", signal_event)
            logger.info(
                f"✅ Publicado evento TradingSignalGenerated com {len(validated_decisions)} decisões validadas."
            )

    async def _execute_trade_handler(self, event: TradingSignalGenerated) -> None:
        """Executa as ordens de um evento ``TradingSignalGenerated``.

        Parameters
        ----------
        event : TradingSignalGenerated
            Evento publicado após a etapa de validação das decisões do oracle,
            contendo a lista de :class:`OracleDecision` a serem executadas.

        Notes
        -----
        A lista de decisões é encaminhada para ``process_decisions`` da
        interface de execução. Em seguida, ``execute_pending_decisions`` envia as
        ordens resultantes ao mercado. Erros são registrados no log.
        """
        logger.info(
            f"ðŏޝ  Handler de Execução recebeu {len(event.decisions)} decisões validadas para executar."
        )
        if not self.execution_interface:
            logger.error(
                "Execution Interface não disponível. Impossível executar ordens.",
                exc_info=True,
            )
            return

        try:
            # A interface de execução é projetada para receber a lista completa de decisões
            await self.execution_interface.process_decisions(event.decisions)
            await self.execution_interface.execute_pending_decisions()
        except Exception as e:
            logger.error(
                f"Erro durante a execução das decisões: {e}",
                exc_info=True,
            )

    async def _initialize_data_collection_layer(
        self, symbols: List[str], timeframes: List[str]
    ) -> None:
        """Inicializa a camada de coleta de dados com parâmetros configurados."""

        logger.info("   📊  Enhanced Data Collector...")
        self.enhanced_data_collector = EnhancedDataCollector(
            symbols,
            timeframes,
            exchange_client=(
                self.market_integration.integration if self.market_integration else None
            ),
            cache_manager=self.cache_manager,
            event_bus=self.event_bus,
        )
        assert self.enhanced_data_collector is not None
        await self.enhanced_data_collector.__aenter__()

        # Real Data Collector (Holographic Events)
        logger.info("   🌀  Real Data Collector (Holographic)...")
        self.real_data_collector = RealDataCollector()
        self.real_data_collector.symbols = symbols
        assert self.real_data_collector is not None
        await self.real_data_collector.__aenter__()

        # YAA Refactor: A inicialização da integração de mercado foi movida
        # para o __init__ para garantir uma instância única.
        # Aqui, apenas validamos se a conexão está funcionando.
        logger.info("   📈  Validating Market Integration...")
        if self.market_integration:
            try:
                # A conexão já é inicializada pelo __aenter__ do EnhancedDataCollector
                # que usa o mesmo objeto. Apenas verificamos o status.
                if not self.market_integration.is_initialized():
                    await self.market_integration.initialize_connection()

                exchange_name = getattr(self.market_integration, "exchange_id", None)
                if not exchange_name and getattr(
                    self.market_integration, "integration", None
                ):
                    exchange_name = getattr(
                        self.market_integration.integration, "exchange_id", None
                    )
                if exchange_name:
                    logger.info(
                        f"   ✅  Market Integration with {exchange_name} is active."
                    )
                else:
                    logger.info(
                        "   ✅  Market Integration active (exchange desconhecida)."
                    )
            except Exception as e:
                logger.error(
                    f"   ❌  Failed to validate Market Integration: {e}", exc_info=True
                )
                self.market_integration = None
                raise
        else:
            logger.warning("    Market Integration not available.")

        logger.info("   ✅  Data Collection Layer inicializada")

    def _initialize_personas(self) -> List[BasePersona]:
        """Initializes persona agents based on configuration."""
        persona_configs = self.config.get("personas", [])
        if not persona_configs:
            logger.warning(
                "Nenhuma configuração de persona encontrada. Usando defaults."
            )
            persona_configs = [
                {
                    "type": "RetailCluster",
                    "name": "RetailInvestor",
                    "module": "qualia.personas.retail_cluster",
                },
                {
                    "type": "MomentumQuant",
                    "name": "MomentumTrader",
                    "module": "qualia.personas.momentum_quant",
                },
            ]

        personas: List[BasePersona] = []
        for p_config in persona_configs:
            try:
                persona_module_path = p_config.get("module")
                persona_type = p_config.get("type")
                persona_name = p_config.get("name")
                persona_config = p_config.get("config", {})
                if not persona_module_path or not persona_type or not persona_name:
                    raise KeyError("Persona 'module', 'type' and 'name' are required.")

                module = importlib.import_module(persona_module_path)
                persona_class = getattr(module, persona_type)
                personas.append(persona_class(persona_name, persona_config))

            except (KeyError, ImportError, AttributeError) as e:
                logger.warning(
                    f"Skipping persona with invalid config: {p_config} - Error: {e}"
                )

        logger.info(f"Initialized {len(personas)} personas.")
        return personas

    def _create_social_simulation_universe(self) -> SocialSimulationUniverse:
        """Creates an instance of the social simulation universe."""
        social_config = self.config.get("social_universe", {})

        # YAA FIX: Extrair parâmetros específicos para o construtor da classe base
        # Em vez de passar o dict `config` inteiro.
        field_size = tuple(social_config.get("field_size", [100, 100]))
        time_steps = social_config.get("time_steps", 200)
        diffusion_rate = social_config.get("diffusion_rate", 0.15)
        feedback_strength = social_config.get("feedback_strength", 0.1)

        universe = SocialSimulationUniverse(
            field_size=field_size,
            time_steps=time_steps,
            diffusion_rate=diffusion_rate,
            feedback_strength=feedback_strength,
        )
        logger.info(
            "[SocialSimUniverse] criado com parametros especificos: "
            f"{field_size} field, {time_steps} steps, diffusion={diffusion_rate}, "
            f"feedback={feedback_strength}"
        )
        return universe

    async def _initialize_analysis_decision_layer(
        self, symbols: List[str], timeframes: List[str], capital: float
    ):
        """Inicializa camada de análise e decisão com instâncias únicas."""

        logger.info(
            "   🌀 Criando instância única do HolographicMarketUniverse [MainUniverse]..."
        )
        holographic_config = self.config.get("holographic", {})
        self.holographic_universe = HolographicMarketUniverse(
            field_size=tuple(holographic_config.get("field_size", [200, 200])),
            time_steps=holographic_config.get("time_steps", 500),
            diffusion_rate=holographic_config.get("diffusion_rate", 0.25),
            feedback_strength=holographic_config.get("feedback_strength", 0.06),
        )
        logger.info(
            "[MainUniverse] criado: "
            f"{self.holographic_universe.field_size} field, "
            f"{self.holographic_universe.time_steps} steps, "
            f"diffusion={self.holographic_universe.diffusion_rate}, "
            f"feedback={self.holographic_universe.feedback_strength}"
        )

        logger.info("   🌌 Criando instância única da UnifiedQUALIAConsciousness...")
        self.consciousness = UnifiedQUALIAConsciousness(
            config=self.config,
            symbols=symbols,
            timeframes=timeframes,
            capital=capital,
            mode=self.mode,
            holographic_universe=self.holographic_universe,
            memory_service=self.memory_service,
        )
        assert self.consciousness is not None

        # --- YAA: Inicialização do Universo Interno ---
        logger.info("    Initializing Internal Universe components...")
        self.personas = self._initialize_personas()
        self.social_universe = self._create_social_simulation_universe()
        self.farsight_engine = HolographicFarsightEngine(
            holographic_universe=self.holographic_universe,
            config=self.config.get("farsight", {}),
            personas=self.personas,
        )
        logger.info("[MainUniverse] reutilizado pelo HolographicFarsightEngine")

        # Initialize SimulationQASTCore if enabled
        if self.config.get("simulation_core", {}).get(
            "enabled", True
        ):  # Habilitado por padrão
            self.simulation_core = SimulationQASTCore(
                config=self.config.get("simulation_core", {}),
                personas=self.personas,
                social_universe=self.social_universe,
                farsight_engine=self.farsight_engine,
            )
            logger.info("   ✅ SimulationQASTCore (Internal Universe) initialized.")
        else:
            self.simulation_core = None
            logger.warning("   SimulationQASTCore (Internal Universe) is disabled.")
        # --- Fim da Inicialização do Universo Interno ---

        # --- YAA TASK-01: Injeção de Dependência para StrategyFactory ---
        shared_context = {
            "risk_manager": getattr(self, "risk_manager", None),
            "market_integration": self.market_integration,
            "qualia_config": self.config,
            "risk_profile": self.config.get("risk_profile", "moderate"),
            "qpm_instance": getattr(self.memory_service, "qpm", None),
            "holographic_universe": self.holographic_universe,
            "consciousness": self.consciousness,
        }

        logger.info("   🧠 Criando instância única do QASTOracleDecisionEngine...")
        self.oracle_engine = QASTOracleDecisionEngine(
            config=self.config,
            symbols=symbols,
            timeframes=timeframes,
            capital=capital,
            market_integration=self.market_integration,
            enhanced_data_collector=self.enhanced_data_collector,
            force_trade_symbol=self.force_trade_symbol,
            shared_context=shared_context,
            memory_service=self.memory_service,
            consciousness_system=self.consciousness,
            metacognition=None,  # Será injetado depois
            simulation_core=self.simulation_core,  # YAA: Injetando o simulation_core
        )
        assert self.oracle_engine is not None
        await self.oracle_engine.initialize()

        # YAA T10: Quebrando a dependência circular
        logger.info("   ðŸ Injetando Oracle na Consciência para completar o ciclo...")
        self.consciousness.set_oracle_engine(self.oracle_engine)
        await self.consciousness.initialize()

        # Adaptive Consciousness Evolution
        logger.info("   🧬  Adaptive Consciousness Evolution...")
        self.adaptive_evolution = AdaptiveConsciousnessEvolution(
            qualia_universe=self.holographic_universe,
            config=self.config.get("adaptive_evolution"),
        )

        # Metacognition
        logger.info("   ðŸ  Metacognition...")
        if self.disable_metacognition:
            logger.warning("    Metacognition desativada por flag de inicialização.")
        elif self.memory_service and self.memory_service.qpm:
            meta_cfg = self.config.get("metacognition", {})
            if self.metacognition_cfg:
                try:
                    meta_cfg = load_env_and_json(json_path=self.metacognition_cfg)
                except Exception as exc:  # pragma: no cover - config errors
                    logger.error(
                        f"   ❌  Falha ao carregar metacognition_config: {exc}",
                        exc_info=True,
                    )
            meta_cfg["require_full_stack"] = True

            self.metacognition = QUALIAMetacognitionTrading(
                adaptive_consciousness_evolution=self.adaptive_evolution,
                qpm_memory=self.memory_service.qpm,
                config=meta_cfg,
                qualia_analysis_module=self.consciousness,
                universe=self.holographic_universe,
            )
            # Injeta a instância criada no Oracle Engine que já foi inicializado
            self.oracle_engine.metacognition = self.metacognition
            logger.info("   ✅  Metacognition inicializada e injetada no Oracle.")
        else:
            logger.warning(
                "    Metacognition não inicializada: MemoryService ou QPM ausente."
            )

        # Cria adapter holográfico com configuração dinâmica
        from qualia.consciousness.holographic_trading_adapter import (
            create_holographic_trading_adapter,
        )

        self.holographic_adapter = create_holographic_trading_adapter(
            trader=self.execution_interface,
            config=self.config,  # Passa a configuração completa para thresholds dinâmicos
        )

        logger.info("   ✅ Analysis & Decision Layer inicializada")

    async def _initialize_execution_risk_layer(self, capital: float):
        """Inicializa camada de execução e risco."""

        # Advanced Risk Manager
        logger.info("   🛡 Advanced Risk Manager...")
        risk_config = self.config.get("risk_management", {})
        self.risk_manager = AdvancedRiskManager(
            initial_capital=capital,
            risk_per_trade_pct=risk_config.get("risk_per_trade", 0.02),
            max_drawdown_pct=risk_config.get("max_drawdown", 0.15),
            max_volatility=risk_config.get("max_volatility", 0.5),
        )

        # Memory Service com QuantumPatternMemory e HolographicMemory
        qpm_config = self.config.get("qpm_config", {})

        if self.memory_service is None:
            qpm = QuantumPatternMemory(
                persistence_path=qpm_config.get(
                    "persistence_path", settings.qpm_memory_file
                ),
                risk_manager=self.risk_manager,
            )
            self.memory_service = MemoryService(
                holographic=HolographicMemory(),
                qpm=qpm,
                event_bus=self.event_bus,
            )
            logger.info(
                "   ✅  MemoryService instanciado com sucesso com QPM e Holographic Memory."
            )
        elif self.memory_service.event_bus is None:
            self.memory_service.event_bus = self.event_bus

        self.memory_service.qpm.risk_manager = self.risk_manager

        # KuCoin Client - usar instância única criada no construtor
        logger.info("    KuCoin Client...")
        if self.kucoin_client and self.kucoin_client.is_initialized():
            try:
                logger.info("   📡 Validando conexão com a KuCoin...")
                ticker = await self.kucoin_client.get_ticker("BTC/USDT")
                if ticker and ticker.get("last"):
                    logger.info(
                        f"   ✅ Conexão com a KuCoin confirmada. BTC/USDT: ${ticker.get('last', 0):,.2f}"
                    )
                else:
                    raise ConnectionError(
                        "Não foi possível obter dados de mercado da KuCoin via ticker."
                    )
            except Exception as e:
                logger.error(
                    f"❌ Falha ao validar conexão com a KuCoin: {e}", exc_info=True
                )
                if self.mode == "live":
                    raise ConnectionError(
                        "Não foi possível validar a conexão com a KuCoin para o modo live."
                    ) from e
        elif self.mode == "live":
            raise ValueError(
                "Cliente KuCoin ausente ou não inicializado para o modo live."
            )
        else:
            logger.warning(
                " Cliente KuCoin não disponível. O sistema não poderá buscar dados de mercado em tempo real."
            )

        # QUALIA Execution Interface
        logger.info("   ðŏޝ QUALIA Execution Interface...")
        self.execution_interface = QUALIAExecutionInterface(
            oracle_engine=self.oracle_engine,
            mode=self.mode,
            exchange_config=self.config.get("exchanges", {}).get("kucoin", {}),
            capital=capital,
            execution_interval=5.0,
        )
        assert self.execution_interface is not None
        await self.execution_interface.initialize()
        if self.holographic_adapter is not None:
            self.holographic_adapter.trader = self.execution_interface

        logger.info("   ✅ Execution & Risk Layer inicializada")

    async def start_trading(self, duration_hours: Optional[float] = None) -> bool:
        """Start all main loops of the trading system.

        Parameters
        ----------
        duration_hours : Optional[float], optional
            Number of hours to run the system. If ``None``, it runs indefinitely.

        Returns
        -------
        bool
            ``True`` if the loops started successfully, ``False`` otherwise.
        """
        logger.info("\n" + "=" * 80)
        logger.info("🚀 INICIANDO LOOPS DO SISTEMA QUALIA 🚀")
        logger.info("=" * 80)
        self.running = True
        self.start_time = time.time()
        end_time = (self.start_time + duration_hours * 3600) if duration_hours else None

        try:
            async with asyncio.TaskGroup() as tg:
                self.task_group = tg

                # YAA-Refactor: Inicia apenas os loops produtores/monitores.
                # Os loops de análise são acionados por eventos.

                # Produtor de Dados
                self.tasks["data_collection"] = tg.create_task(
                    self._data_collection_loop(self)
                )

                # Monitores e outros loops independentes
                self.tasks["monitoring"] = tg.create_task(self._monitoring_loop(self))
                self.tasks["safety_monitoring"] = tg.create_task(
                    self._safety_monitoring_loop(self)
                )
                self.tasks["consciousness"] = tg.create_task(
                    self._consciousness_loop(self)
                )
                # O loop de execução agora é apenas para monitorar PnL, não para executar ordens.
                self.tasks["execution_pnl_monitor"] = tg.create_task(
                    self._execution_cycle_loop(self)
                )

                if self.metacognition:
                    self.tasks["metacognition"] = tg.create_task(
                        self._metacognition_loop(self)
                    )

                logger.info("✅ Todos os loops principais foram iniciados.")

                # Loop de gerenciamento principal
                while self.running and not self.shutdown_requested:
                    if end_time and time.time() >= end_time:
                        logger.info(
                            " Tempo de execução atingido. Iniciando shutdown..."
                        )
                        self.shutdown_requested = True
                        break
                    await asyncio.sleep(1)

        except Exception as e:
            logger.error(
                f"❌ Erro fatal no grupo de tarefas: {e}",
                exc_info=True,
            )
            self.emergency_stop = True
            self.shutdown_requested = True
            return False
        finally:
            self.running = False
            logger.info(" Loops principais encerrados.")
            # O shutdown dos componentes será chamado no __aexit__

        return True

    def _calculate_performance_based_threshold(self) -> float:
        """
        Calcula um limiar de confiança dinâmico baseado no desempenho histórico.
        Um bom desempenho (win rate > 50%) relaxa o limiar, enquanto um
        desempenho ruim o torna mais rigoroso.
        """
        base_threshold = self.config.get("signal_approval", {}).get(
            "min_confidence", 0.10
        )
        min_trades_for_adjustment = self.config.get("signal_approval", {}).get(
            "min_trades_for_adjustment", 10
        )
        sensitivity_factor = self.config.get("signal_approval", {}).get(
            "performance_sensitivity_factor", 0.2
        )

        total_trades = len(self.trade_history)
        if total_trades < min_trades_for_adjustment:
            return base_threshold  # Usa o threshold base se não houver histórico suficiente

        wins = sum(1 for trade in self.trade_history if trade.get("pnl", 0) > 0)
        win_rate = wins / total_trades

        # O ajuste é proporcional à diferença entre o win rate e 50%
        # Win rate > 0.5 -> ajuste negativo (reduz o threshold)
        # Win rate < 0.5 -> ajuste positivo (aumenta o threshold)
        adjustment = (0.5 - win_rate) * sensitivity_factor

        dynamic_threshold = base_threshold + adjustment

        # Garante que o threshold permaneça em uma faixa razoável (e.g., 0.05 a 0.5)
        final_threshold = max(0.05, min(dynamic_threshold, 0.5))

        logger.debug(
            f"🧠 Calibração de Confiança por Performance: "
            f"WinRate={win_rate:.2%} ({wins}/{total_trades} trades), "
            f"Ajuste={adjustment:+.3f}, "
            f"Threshold Final={final_threshold:.4f}"
        )

        return final_threshold

    def _enhanced_risk_assessment(self, decision, symbol_data=None) -> Dict[str, Any]:
        """Avaliação de risco aprimorada usando dados em tempo real e performance histórica."""

        # Obtém configurações de aprovação de sinais (com fallbacks para modo conservador)
        signal_approval_config = self.config.get("signal_approval", {})

        # Thresholds configuráveis
        min_volume_ratio = signal_approval_config.get("min_volume_ratio", 0.5)
        max_volatility = signal_approval_config.get("max_volatility", 5.0)
        aggressive_mode = signal_approval_config.get("enable_aggressive_mode", False)

        # NOVO: Calcular limiar dinâmico baseado em performance
        min_confidence = self._calculate_performance_based_threshold()

        # Log para debug
        logger.debug(
            f"🛡  Enhanced Risk Assessment (Aggressive: {aggressive_mode}): "
            f"Confidence Dinâmica ≥ {min_confidence:.4f}, "
            f"Volume ≥ {min_volume_ratio}, Volatilidade ≤ {max_volatility}"
        )

        # 1. Validação básica de decisão
        if not decision or not hasattr(decision, "confidence"):
            return {"approved": False, "reason": "Decisão inválida"}

        # 2. Volatilidade alta (usando threshold configurável)
        if (
            symbol_data
            and hasattr(symbol_data, "volatility")
            and symbol_data.volatility is not None
            and symbol_data.volatility > max_volatility
        ):
            return {
                "approved": False,
                "reason": f"Volatilidade alta: {symbol_data.volatility:.2f}% > {max_volatility}%",
            }

        # 3. Volume insuficiente (usando threshold configurável)
        if (
            hasattr(symbol_data, "volume_ratio")
            and symbol_data.volume_ratio is not None
            and symbol_data.volume_ratio < min_volume_ratio
        ):
            return {
                "approved": False,
                "reason": f"Volume baixo: {symbol_data.volume_ratio:.2f}x < {min_volume_ratio}x",
            }

        # 4. Confidence muito baixa (usando threshold dinâmico)
        if decision.confidence < min_confidence:
            return {
                "approved": False,
                "reason": f"Confidence baixa: {decision.confidence:.4f} < {min_confidence:.4f} (limiar dinâmico por performance)",
            }

        # 5. Validações adicionais em modo agressivo (mais permissivo)
        risk_score = 1.0 - decision.confidence
        if aggressive_mode:
            # Em modo agressivo, aceita riscos mais altos
            risk_score *= 0.5  # Reduz o score de risco pela metade

        return {
            "approved": True,
            "reason": f"Validação {'agressiva' if aggressive_mode else 'padrão'} aprovada",
            "risk_score": risk_score,
            "enhanced_validation": True,
            "aggressive_mode": aggressive_mode,
        }

    async def _collect_performance_metrics(self):
        """Coleta métricas de performance abrangentes.

        Retorna um dicionário com as principais métricas do sistema,
        utilizando as chaves:

        - ``consciousness_status['consciousness_state']['consciousness_level']``
        - ``oracle_status['decision_history_count']``
        - ``execution_status['total_trades']``
        - ``field_summary['field_energy']`` e ``field_summary['field_entropy']``
        """

        def _fetch(status: Dict[str, Any], key: str) -> Any:
            if status and key in status:
                return status[key]
            if hasattr(self, "zero_metric_counts"):
                self.zero_metric_counts[key] += 1
            fallback_used.append(key)
            return 0

        # Status básico
        consciousness_status = (
            self.consciousness.get_unified_status() if self.consciousness else {}
        )
        oracle_status = self.oracle_engine.get_status() if self.oracle_engine else {}
        execution_status = (
            self.execution_interface.get_status() if self.execution_interface else {}
        )

        # Métricas holográficas
        field_summary = (
            self.holographic_universe.get_field_summary()
            if self.holographic_universe
            else {}
        )

        fallback_used: list[str] = []

        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "uptime_hours": (time.time() - self.start_time) / 3600,
            "consciousness_level": consciousness_status.get(
                "consciousness_state", {}
            ).get("consciousness_level", 0),
            "oracle_decisions": oracle_status.get("decision_history_count", 0),
            "executed_decisions": execution_status.get("total_trades", 0),
            "current_capital": execution_status.get("current_capital", 0),
            "total_pnl": execution_status.get("total_pnl", 0),
            "open_positions": execution_status.get("open_positions", 0),
            "win_rate": execution_status.get("win_rate", 0),
            "drawdown": execution_status.get("current_drawdown", 0),
            "field_energy": field_summary.get("field_energy", 0),
            "field_entropy": field_summary.get("field_entropy", 0),
            "holographic_patterns": (
                len(self.holographic_metrics[-1:]) if self.holographic_metrics else 0
            ),
            "fallback_metrics": fallback_used,
        }

    async def _perform_safety_checks(self):
        """Realiza verificações de segurança baseadas na configuração carregada."""

        if not self.execution_interface:
            return {"safe": True, "reason": "No execution interface"}

        status = self.execution_interface.get_status()

        current_drawdown = status.get("current_drawdown", 0)
        total_pnl_pct = status.get("total_pnl_pct", 0)
        open_positions = status.get("open_positions", 0)

        # Verifica perda máxima diária
        if abs(total_pnl_pct) > self.max_daily_loss:
            return {
                "safe": False,
                "reason": f"Perda diária máxima atingida: {total_pnl_pct:.2%}",
            }

        # Verifica drawdown máximo
        if current_drawdown > self.max_drawdown:
            return {
                "safe": False,
                "reason": f"Drawdown máximo atingido: {current_drawdown:.2%}",
            }

        # Verifica número de posições
        if open_positions > self.max_positions:
            return {
                "safe": False,
                "reason": f"Muitas posições abertas: {open_positions}",
            }

        return {"safe": True, "reason": "All checks passed"}

    async def _emergency_stop(self, reason: str):
        """Ativa o mecanismo de parada de emergência."""
        if self.emergency_stop:
            return

        logger.error(
            f"\n🚨 PARADA DE EMERGÊNCIA: {reason}",
            exc_info=True,
        )
        self.emergency_stop = True
        self.shutdown_requested = True

        if self.execution_interface:
            logger.error("ðŸ Tentando fechar todas as posições...", exc_info=True)
            try:
                # Chama o método de shutdown que fecha posições e para o loop
                await self.execution_interface.shutdown()
                logger.info("✅ Posições fechadas e interface de execução parada.")
            except Exception as e:
                logger.error(
                    f"❌ Erro no shutdown da interface de execução: {e}", exc_info=True
                )

        # Executa a rotina de encerramento
        await self._shutdown()

        # Cancela todas as tarefas pendentes
        await self._cancel_pending_tasks()

        logger.info("System shutdown complete.")

    def _log_comprehensive_status(self, entry):
        """Log abrangente do status do sistema."""

        logger.info(f"\n📊 STATUS COMPLETO ({entry['timestamp'][:19]})")
        self._log_final_summary(entry)
        if entry.get("fallback_metrics"):
            logger.info(
                "   ⚠ Valores de fallback para: " + ", ".join(entry["fallback_metrics"])
            )
        else:
            logger.info("   ✅ Valores reais coletados")
        logger.info(f"   📊 Posições: {entry['open_positions']}")
        logger.info(
            f"   🌀 Campo Holográfico: E={entry['field_energy']:.3f}, S={entry['field_entropy']:.3f}"
        )

        # Adiciona métricas quantum se disponíveis (alinhado com deploy_enhanced)
        if hasattr(self, "quantum_states") and self.quantum_states:
            quantum_encoded_count = sum(
                1
                for data in self.quantum_states.values()
                if data.get("quantum_encoded", False)
            )

            # Estatísticas detalhadas dos quantum encoders
            rsi_encoded = sum(
                1
                for data in self.quantum_states.values()
                if data.get("rsi_quantum_state") is not None
            )
            volume_encoded = sum(
                1
                for data in self.quantum_states.values()
                if data.get("volume_quantum_state") is not None
            )

            logger.info(
                f"   🌀 Quantum States: {quantum_encoded_count}/{len(self.quantum_states)} símbolos encoded "
                f"(RSI: {rsi_encoded}, Volume: {volume_encoded})"
            )

            # Média dos indicadores técnicos
            rsi_values = [
                data.get("rsi")
                for data in self.quantum_states.values()
                if data.get("rsi") is not None
            ]
            vol_ratios = [
                data.get("volume_ratio")
                for data in self.quantum_states.values()
                if data.get("volume_ratio") is not None
            ]
            volatilities = [
                data.get("volatility")
                for data in self.quantum_states.values()
                if data.get("volatility") is not None
            ]

            if rsi_values:
                avg_rsi = sum(rsi_values) / len(rsi_values)
                logger.info(
                    f"   📈 RSI Médio: {avg_rsi:.1f} (range: {min(rsi_values):.1f}-{max(rsi_values):.1f})"
                )

            if vol_ratios:
                avg_vol_ratio = sum(vol_ratios) / len(vol_ratios)
                logger.info(f"   📊 Volume Ratio Médio: {avg_vol_ratio:.2f}x")

            if volatilities:
                avg_volatility = sum(volatilities) / len(volatilities)
                logger.info(f"   📈 Volatilidade Média: {avg_volatility:.2f}%")

        # Métricas de enhanced risk assessment
        if self.decision_metrics:
            latest_metrics = self.decision_metrics[-1]
            rejection_rate = latest_metrics.get("rejection_rate", 0)
            validated_decisions = latest_metrics.get("validated_decisions", 0)

            logger.info(
                f"   🛡 Enhanced Risk: {validated_decisions} aprovadas, rejection_rate={rejection_rate:.1%}"
            )

        # Alertas para live trading (alinhado com deploy_enhanced)
        if self.mode == "live" and entry["total_pnl"] < 0:
            pnl_pct = (
                (entry["total_pnl"] / entry["current_capital"]) * 100
                if entry["current_capital"] > 0
                else 0
            )
            if abs(pnl_pct) > 2:
                logger.warning(f"    ALERTA: Perda de {pnl_pct:.1f}%")

        # Status dos componentes principais
        logger.info(
            "   ðŸ§ Componentes: Enhanced Data Collector, Holographic Universe, Oracle Engine, Risk Manager"
        )
        if hasattr(self, "kucoin_client") and self.kucoin_client:
            logger.info("    Exchange: KuCoin conectado")
        else:
            logger.info("    Exchange: Modo demo/paper trading")

    def _log_final_summary(self, entry):
        """Loga um conjunto resumido de métricas de performance."""

        logger.info(f"    Uptime: {entry['uptime_hours']:.1f}h")
        logger.info(f"   🧠 Consciência: {entry['consciousness_level']:.2f}")
        logger.info(f"   ðŏޝ Oracle: {entry['oracle_decisions']} decisões")
        logger.info(f"   💰 Capital: ${entry['current_capital']:,.2f}")
        logger.info(f"   💹 PnL: ${entry['total_pnl']:,.2f}")
        logger.info(f"   📊 Win Rate: {entry['win_rate']:.1%}")
        logger.info(f"   🌀 Energia Holográfica: {entry['field_energy']:.3f}")
        logger.info(
            f"   📈 Padrões Detectados: {sum(m.get('patterns_detected', 0) for m in self.holographic_metrics)}"
        )

    async def _shutdown(self) -> None:
        """Stop all system components gracefully.

        Returns
        -------
        None
            This method does not return anything.
        """
        logger.info("\n🛑 Parando sistema com arquitetura completa...")

        # YAA-Refactor: Publica o evento de shutdown para notificar todos os componentes.
        logger.info("🚌 Publicando evento de shutdown do sistema...")
        self.event_bus.publish(
            "system.shutdown", SystemShutdown(reason="Shutdown solicitado.")
        )
        await asyncio.sleep(0.1)  # Pequena pausa para o evento propagar

        # YAA-Refactor: Desliga o barramento de eventos, cancelando tarefas pendentes.
        # Garante encerramento do barramento de eventos sem exceções
        try:
            await self.event_bus.shutdown()
        except Exception as eb_exc:
            logger.error(
                f"Erro encerrando EventBus: {eb_exc}",
                exc_info=True,
            )

        # Para componentes em ordem reversa
        if self.kucoin_client:
            try:
                await self.kucoin_client.shutdown()
                # YAA: Garantir fechamento explícito do conector aiohttp/ccxt
                if hasattr(self.kucoin_client, "close"):
                    await self.kucoin_client.close()
                logger.info("✅ KuCoin client fechado")
            except Exception as e:
                logger.error(
                    f"Erro fechando KuCoin: {e}",
                    exc_info=True,
                )

        # Aguarda um pouco para garantir que todas as sessões sejam fechadas
        await asyncio.sleep(0.1)

        if self.consciousness:
            try:
                await self.consciousness.stop_consciousness()
                await self.consciousness.shutdown()
                logger.info("✅ Consciência parada")
            except Exception as e:
                logger.error(
                    f"Erro parando consciência: {e}",
                    exc_info=True,
                )

        if self.execution_interface:
            try:
                await self.execution_interface.shutdown()
                logger.info("✅ Execution Interface encerrada")

                # YAA: Garantir o fechamento da conexão de mercado
                if self.market_integration:
                    await self.market_integration.close()
                    logger.info("✅ Market Integration encerrada")

                # Aguarda um pouco para garantir que todas as sessões sejam fechadas
                await asyncio.sleep(0.3)
                logger.info("✅ Aguardado tempo para fechamento de sessões")
            except Exception as e:
                logger.error(
                    f"Erro parando execução: {e}",
                    exc_info=True,
                )

        if self.oracle_engine:
            try:
                await self.oracle_engine.shutdown()
                logger.info("✅ Oracle engine parado")
            except Exception as e:
                logger.error(
                    f"Erro parando oracle: {e}",
                    exc_info=True,
                )

        if self.holographic_universe:
            try:
                await self.holographic_universe.shutdown()
                logger.info("✅ Universo holográfico parado")
            except Exception as e:
                logger.error(
                    f"Erro parando holográfico: {e}",
                    exc_info=True,
                )

        if self.enhanced_data_collector:
            try:
                await self.enhanced_data_collector.__aexit__(None, None, None)
                logger.info("✅ Enhanced Data Collector parado")
            except Exception as e:
                logger.error(
                    f"Erro parando enhanced data collector: {e}",
                    exc_info=True,
                )

        if self.real_data_collector:
            try:
                await self.real_data_collector.__aexit__(None, None, None)
                logger.info("✅ Real Data Collector parado")
            except Exception as e:
                logger.error(
                    f"Erro parando real data collector: {e}",
                    exc_info=True,
                )

        # Aguarda um pouco para garantir que todas as sessões sejam fechadas
        await asyncio.sleep(1.0)
        logger.info("✅ Aguardado tempo para fechamento completo das sessões")

        # Salva relatório final
        self._save_comprehensive_report()

        logger.info("✅ Sistema parado com sucesso!")

    async def _cancel_pending_tasks(self) -> None:
        """Cancel and await all tasks registered in ``self.tasks``."""

        tasks = list(self.tasks.values())
        if tasks:
            logger.info(f"Canceling {len(tasks)} running tasks...")
            for task in tasks:
                task.cancel()
            await asyncio.gather(*tasks, return_exceptions=True)
            logger.info("All tasks have been canceled.")
        self.tasks.clear()

    def _save_comprehensive_report(self):
        """Salva relatório abrangente final."""

        try:
            os.makedirs("reports", exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_path = f"reports/qualia_complete_{self.mode}_report_{timestamp}.json"

            # Calcula estatísticas finais
            final_status = {}
            if self.consciousness:
                final_status.update(self.consciousness.get_unified_status())
            if self.execution_interface:
                final_status.update(self.execution_interface.get_status())

            report = {
                "session_info": {
                    "start_time": datetime.fromtimestamp(self.start_time).isoformat() if isinstance(self.start_time, (int, float)) else self.start_time.isoformat(),
                    "end_time": datetime.now().isoformat(),
                    "duration_hours": (time.time() - (self.start_time if isinstance(self.start_time, (int, float)) else self.start_time.timestamp())) / 3600,
                    "mode": self.mode,
                    "emergency_stop": self.emergency_stop,
                    "architecture": "Complete QUALIA Trading System",
                },
                "final_status": final_status,
                "performance_summary": {
                    "total_entries": len(self.performance_data),
                    "avg_consciousness": sum(
                        e.get("consciousness_level", 0) for e in self.performance_data
                    )
                    / max(len(self.performance_data), 1),
                    "max_consciousness": max(
                        (
                            e.get("consciousness_level", 0)
                            for e in self.performance_data
                        ),
                        default=0,
                    ),
                    "final_pnl": final_status.get("total_pnl", 0),
                    "max_positions": max(
                        (e.get("open_positions", 0) for e in self.performance_data),
                        default=0,
                    ),
                    "avg_field_energy": sum(
                        e.get("field_energy", 0) for e in self.performance_data
                    )
                    / max(len(self.performance_data), 1),
                    "total_oracle_decisions": sum(
                        e.get("oracle_decisions", 0) for e in self.performance_data
                    ),
                    "total_holographic_patterns": sum(
                        e.get("holographic_patterns", 0) for e in self.performance_data
                    ),
                },
                "performance_history": self.performance_data[
                    -100:
                ],  # Últimas 100 entradas
                "holographic_metrics": self.holographic_metrics[
                    -50:
                ],  # Últimas 50 entradas
                "decision_metrics": self.decision_metrics[-50:],  # Últimas 50 entradas
                "config_path": self.config_path,
                "timing_configuration": self.timing,
            }

            with open(report_path, "w", encoding="utf-8") as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            logger.info(f"📄 Relatório completo salvo: {report_path}")

            # Mostra resumo final abrangente
            if self.performance_data:
                final_entry = self.performance_data[-1]
                logger.info("\n📈 RESUMO FINAL COMPLETO:")
                self._log_final_summary(final_entry)

        except Exception as e:
            logger.error(
                f"Erro salvando relatório: {e}",
                exc_info=True,
            )

    async def __aenter__(self):
        """Inicializa o sistema para uso com ``async with``.

        Returns
        -------
        QUALIATradingSystem
            Instância pronta para operação.
        """
        try:
            if not await self.initialize():
                raise RuntimeError("Falha na inicialização do QUALIATradingSystem")
            return self
        except (KeyboardInterrupt, asyncio.CancelledError):
            logger.warning(
                "Inicialização interrompida pelo usuário. Limpando recursos..."
            )
            # Tenta fechar recursos já inicializados
            await self._emergency_cleanup()
            raise
        except Exception as e:
            logger.error(
                f"❌ Erro durante inicialização: {e}",
                exc_info=True,
            )
            await self._emergency_cleanup()
            raise

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Finaliza o sistema ao sair do contexto assíncrono."""
        logger.info("Exiting context manager, ensuring graceful shutdown...")

        # Garante que o shutdown seja solicitado
        self.running = False
        self.shutdown_requested = True

        # Cancela todas as tarefas pendentes ANTES de fazer o shutdown dos componentes
        await self._cancel_pending_tasks()

        # Executa a rotina de encerramento dos componentes
        await self._shutdown()

        logger.info("System shutdown complete.")

    async def _emergency_cleanup(self):
        """Limpa recursos durante falha de inicialização ou interrupção.

        Este método é chamado quando a inicialização falha ou é interrompida,
        garantindo que recursos parcialmente inicializados sejam liberados.
        """
        logger.info("🧹 Executando limpeza de emergência...")

        close_tasks = []
        kucoin_closed = False
        market_closed = False

        # Fecha KuCoin client se foi inicializado
        if hasattr(self, "kucoin_client") and self.kucoin_client:
            close_func = getattr(self.kucoin_client, "close", None)
            if close_func is not None:
                try:
                    if asyncio.iscoroutinefunction(close_func):
                        close_tasks.append(close_func())
                    else:
                        close_func()
                    kucoin_closed = True
                except Exception as e:
                    logger.debug(f"Erro fechando KuCoin: {e}")

        # Fecha market integration
        if hasattr(self, "market_integration") and self.market_integration:
            try:
                close_tasks.append(self.market_integration.close())
                market_closed = True
            except Exception as e:
                logger.debug(f"Erro fechando market integration: {e}")

        if close_tasks:
            await asyncio.gather(*close_tasks, return_exceptions=True)
            if kucoin_closed:
                logger.info("✅ KuCoin client fechado")
            if market_closed:
                logger.info("✅ Market integration fechado")

        # Para data collectors
        if hasattr(self, "enhanced_data_collector") and self.enhanced_data_collector:
            try:
                await self.enhanced_data_collector.__aexit__(None, None, None)
                logger.info("✅ Enhanced data collector parado")
            except Exception as e:
                logger.debug(f"Erro parando enhanced collector: {e}")

        if hasattr(self, "real_data_collector") and self.real_data_collector:
            try:
                await self.real_data_collector.__aexit__(None, None, None)
                logger.info("✅ Real data collector parado")
            except Exception as e:
                logger.debug(f"Erro parando real collector: {e}")

        # Shutdown event bus se existir
        if hasattr(self, "event_bus") and self.event_bus:
            try:
                await self.event_bus.shutdown()
                logger.info("✅ Event bus encerrado")
            except Exception as e:
                logger.debug(f"Erro encerrando event bus: {e}")

        # Aguarda para garantir fechamento
        await asyncio.sleep(0.5)
        logger.info("✅ Limpeza de emergência concluída")

    async def _data_collection_loop(self):
        """Loop de coleta de dados (PRODUTOR)."""
        logger.info("📡 Data Collection Loop (Produtor) iniciado")
        while self.running and not self.shutdown_requested:
            try:
                # Coleta todos os dados
                enhanced_data = (
                    await self.enhanced_data_collector.collect_enhanced_market_data()
                )
                news_events = (
                    await self.enhanced_data_collector.collect_news_events()
                )  # Usar collector unificado

                # Cria um pacote de dados unificado
                data_packet = {
                    "timestamp": time.time(),
                    "enhanced_market_data": enhanced_data,
                    "news_events": news_events,
                    "type": "market_update",
                }

                # Coloca na fila para o consumidor
                await self.data_queue.put(data_packet)
                logger.info(
                    f"📬 Pacote de dados colocado na fila (contém {len(enhanced_data)} enhanced data points)"
                )

                await asyncio.sleep(self.timing["data_collection_interval"])
            except Exception as e:
                logger.error(f"Erro na coleta de dados: {e}", exc_info=True)
                await asyncio.sleep(30.0)

    async def _decision_cycle_loop(self):
        """Loop de ciclo de decisão (CONSUMIDOR)."""
        logger.info("🧠 Decision Cycle Loop (Consumidor) iniciado")
        while self.running and not self.shutdown_requested:
            try:
                # Obtém o pacote de dados mais recente da fila
                data_packet = await self.data_queue.get()
                logger.info("📦 Pacote de dados recebido da fila.")

                enhanced_data = data_packet.get("enhanced_market_data", [])
                news_events = data_packet.get("news_events", [])

                # 1. Atualiza o Universo Holográfico com os novos dados
                holographic_events = (
                    self.enhanced_data_collector.convert_to_holographic_events(
                        enhanced_data, news_events, self.holographic_universe.field_size
                    )
                )
                for event in holographic_events:
                    await self.holographic_universe.inject_holographic_event(event)

                # 2. Alimenta o Oracle Engine com os dados (sem que ele precise buscar)
                oracle_decisions = await self.oracle_engine.generate_decisions(
                    enhanced_market_data=enhanced_data
                )

                # ... (resto da lógica de combinação, validação e execução) ...

                self.data_queue.task_done()
                await asyncio.sleep(self.timing["decision_cycle_interval"])
            except Exception as e:
                logger.error(f"❌ ERRO CRÍTICO no ciclo de decisão: {e}", exc_info=True)
                await asyncio.sleep(10.0)


def update_recursive(d, u):
    """Atualiza ``d`` com os valores de ``u`` de forma recursiva.

    Parameters
    ----------
    d : dict
        Dicionário base a ser atualizado.
    u : dict
        Dicionário com valores de atualização.

    Returns
    -------
    dict
        Dicionário ``d`` após a atualização.
    """
    # YAA: Verificação de segurança - garantir que d é um dicionário válido
    if d is None:
        d = {}
    if not isinstance(d, dict):
        logger.warning(
            f"update_recursive: d não é um dicionário, convertendo: {type(d)}"
        )
        d = {}

    # YAA: Verificação de segurança - garantir que u é um dicionário válido
    if u is None:
        logger.warning("update_recursive: u é None, retornando d inalterado")
        return d
    if not isinstance(u, dict):
        logger.warning(f"update_recursive: u não é um dicionário: {type(u)}")
        return d

    for k, v in u.items():
        if isinstance(v, collections.abc.Mapping):
            # YAA: Garantir que d[k] seja um dicionário antes da recursão
            current_value = d.get(k)
            if current_value is None or not isinstance(current_value, dict):
                current_value = {}
            d[k] = update_recursive(current_value, v)
        else:
            d[k] = v
    return d


async def main() -> int:
    """Função principal para executar o sistema de trading."""
    parser = argparse.ArgumentParser(description="QUALIA Trading System")
    parser.add_argument(
        "--log-level",
        type=str,
        default=os.getenv("QUALIA_LOG_LEVEL", "INFO"),
        help="Nível de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)",
    )
    parser.add_argument(
        "--config",
        type=str,
        default="config/unified_qualia_consciousness_config.yaml",
        help="Caminho para o arquivo de configuração YAML principal.",
    )
    parser.add_argument(
        "--mode",
        type=str,
        default="paper_trading",
        choices=["paper_trading", "live"],
        help="Modo de operação",
    )
    parser.add_argument(
        "--hours", type=float, default=None, help="Duração da execução em horas"
    )
    parser.add_argument(
        "--aggressive-test",
        action="store_true",
        help="Carrega a configuração de teste agressiva para forçar a abertura de posições.",
    )
    parser.add_argument(
        "--force-trade-on",
        type=str,
        default=None,
        help="Força um sinal de COMPRA para o símbolo especificado (ex: BTCUSDT) para fins de teste.",
    )
    parser.add_argument(
        "--disable-metacognition",
        action="store_true",
        help="Desativa completamente a camada de metacognição.",
    )
    parser.add_argument(
        "--metacognition-config",
        type=str,
        default=None,
        help="Caminho opcional para configuração específica de metacognição.",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Habilita logs detalhados de bibliotecas externas",
    )
    args = parser.parse_args()

    # Mover a configuração de logging para depois do parsing dos argumentos
    # para que o --log-level possa ser usado.
    setup_logging(config={"level": args.log_level})
    logger.info(
        "================================================================================"
    )
    logger.info(
        "==                      INICIANDO SISTEMA QUALIA TRADING                        =="
    )
    logger.info(
        "================================================================================"
    )

    configure_library_loggers(args.debug)

    # YAA-Refactor: Remover a criação explícita do event_bus aqui,
    # ele é gerenciado como um singleton pelo próprio módulo.

    try:
        # Carrega a configuração base primeiro
        logger.info(f"📋 Carregando configuração base de: {args.config}")
        base_config = load_env_and_json(json_path=args.config)
        base_config["config_source_path"] = args.config  # Adiciona o path ao dicionário

        # Se o modo agressivo estiver ativado, carrega e mescla
        if args.aggressive_test:
            logger.warning("🚀 MODO DE TESTE AGRESSIVO ATIVADO! NÃO USE EM PRODUÇÃO!")
            aggressive_config_path = Path("config/aggressive_test_config.yaml")
            logger.info(
                f"🌀 Mesclando com configuração agressiva de: {aggressive_config_path}"
            )
            if aggressive_config_path.exists():
                with open(aggressive_config_path, "r") as f:
                    aggressive_config = yaml.safe_load(f)
                    # Mescla recursivamente, com a config agressiva tendo prioridade
                    final_config = update_recursive(base_config, aggressive_config)
                final_config["config_source_path"] = str(
                    aggressive_config_path
                )  # Sobrescreve o path
            else:
                logger.error(
                    f"❌ Arquivo de configuração agressiva não encontrado: {aggressive_config_path}"
                )
                final_config = base_config
        else:
            final_config = base_config

        # Inicia o sistema com a configuração final e a flag de força
        async with QUALIATradingSystem(
            config=final_config,
            config_path=final_config.get(
                "config_source_path", "config/unified_qualia_consciousness_config.yaml"
            ),
            hours=args.hours,
            mode=args.mode,
            aggressive_test=args.aggressive_test,
            force_trade_symbol=args.force_trade_on,
            disable_metacognition=args.disable_metacognition,
            metacognition_config=args.metacognition_config,
        ) as system:
            if args.force_trade_on:
                logger.warning(
                    f"🚀 MODO DE FORÇA DE TRADE ATIVADO PARA {args.force_trade_on}! NÃO USE EM PRODUÇÃO!"
                )
            await system.start_trading(duration_hours=args.hours)

        logger.info("✅ Execução concluída com sucesso.")
        return 0
    except RuntimeError as e:
        # YAA-Refactor: Tratamento de exceção robusto para garantir visibilidade da falha.
        # Imprime no stderr para contornar qualquer problema de configuração de logging.
        import traceback

        print(f"❌ ERRO CRÍTICO DE INICIALIZAÇÃO: {e}", file=sys.stderr)
        traceback.print_exc()
        return 1
    except Exception as e:
        # YAA-Refactor: Captura genérica com o mesmo tratamento robusto.
        import traceback

        print(f"❌ ERRO INESPERADO NA EXECUÇÃO: {e}", file=sys.stderr)
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\n🛑 Sistema encerrado pelo usuário (KeyboardInterrupt).")
