"""Sistema de Logging de Métricas em Tempo Real para Trading.

Este módulo implementa um sistema robusto de logging que captura todas as métricas
relevantes das decisões de trading em tempo real, permitindo análise post-mortem
e calibração do sistema.
"""

import json
import os
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import threading
import queue
import time
from contextlib import contextmanager

try:
    from .logger import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

logger = get_logger(__name__)

@dataclass
class TimeframeMetrics:
    """Métricas de um timeframe específico."""
    timeframe: str
    signal: str
    confidence: float
    signal_strength: float
    hype_momentum: float
    holographic_boost: float
    tsvf_validation: float
    data_periods: int
    timestamp: str

@dataclass
class ConsolidatedMetrics:
    """Métricas do sinal consolidado."""
    final_signal: str
    final_confidence: float
    convergence_score: float
    primary_timeframe: str
    supporting_timeframes: List[str]
    reasoning: str
    timestamp: str

@dataclass
class TradingDecisionMetrics:
    """Métricas completas de uma decisão de trading."""
    # Identificação
    decision_id: str
    symbol: str
    timestamp: str
    
    # Tipo de log: 'SIGNAL_TRACKING' ou 'TRADE_EXECUTION'
    log_type: str
    
    # Dados de mercado
    current_price: float
    volume: Optional[float]
    
    # Métricas por timeframe
    timeframe_metrics: List[TimeframeMetrics]
    
    # Métricas consolidadas
    consolidated_metrics: ConsolidatedMetrics
    
    # Decisão final
    trade_executed: bool
    execution_reason: str
    rejection_reason: Optional[str]
    
    # Níveis de trading
    stop_loss: Optional[float]
    take_profit: Optional[float]
    
    # Contexto de risco
    position_size: Optional[float]
    risk_percentage: Optional[float]
    
    # Métricas de sistema
    processing_time_ms: float
    data_quality_score: float
    
    # Contexto adicional
    market_conditions: Dict[str, Any]
    system_state: Dict[str, Any]

class RealTimeMetricsLogger:
    """Logger de métricas em tempo real com persistência assíncrona."""
    
    def __init__(self,
                 log_directory: str = "logs/metrics",
                 max_queue_size: int = 1000,
                 flush_interval: float = 1.0,
                 enable_compression: bool = True,
                 filter_hold_positions: bool = True):
        """
        Inicializa o logger de métricas.

        Args:
            log_directory: Diretório para salvar os logs
            max_queue_size: Tamanho máximo da fila de logs
            flush_interval: Intervalo de flush em segundos
            enable_compression: Se deve comprimir logs antigos
            filter_hold_positions: Se deve filtrar posições HOLD dos logs
        """
        self.log_directory = Path(log_directory)
        self.log_directory.mkdir(parents=True, exist_ok=True)

        self.max_queue_size = max_queue_size
        self.flush_interval = flush_interval
        self.enable_compression = enable_compression
        self.filter_hold_positions = filter_hold_positions
        
        # Fila thread-safe para logs
        self.log_queue = queue.Queue(maxsize=max_queue_size)
        
        # Thread de processamento
        self.processing_thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        
        # Estatísticas
        self.stats = {
            'logs_written': 0,
            'logs_dropped': 0,
            'last_flush': None,
            'errors': 0
        }
        
        # Arquivo atual
        self.current_file = None
        self.current_date = None
        
        logger.info(f"RealTimeMetricsLogger inicializado: {self.log_directory}")
    
    def start(self):
        """Inicia o logger assíncrono."""
        if self.is_running:
            logger.warning("Logger já está rodando")
            return
        
        self.is_running = True
        self.stop_event.clear()
        
        self.processing_thread = threading.Thread(
            target=self._processing_loop,
            name="MetricsLogger",
            daemon=True
        )
        self.processing_thread.start()
        
        logger.info("RealTimeMetricsLogger iniciado")
    
    def stop(self):
        """Para o logger e processa logs pendentes."""
        if not self.is_running:
            return
        
        logger.info("Parando RealTimeMetricsLogger...")
        
        self.stop_event.set()
        
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5.0)
        
        # Processa logs restantes
        self._flush_remaining_logs()
        
        if self.current_file:
            self.current_file.close()
            self.current_file = None
        
        self.is_running = False
        logger.info("RealTimeMetricsLogger parado")
    
    def log_trading_decision(self, metrics: TradingDecisionMetrics):
        """Registra métricas de uma decisão de trading."""
        try:
            if not self.is_running:
                logger.warning("Logger não está rodando - iniciando automaticamente")
                self.start()

            # Filtro: Não salva logs de posições HOLD (se habilitado)
            if self.filter_hold_positions and self._is_hold_decision(metrics):
                logger.debug(f"Ignorando log de posição HOLD para {metrics.symbol} (decision_id: {metrics.decision_id})")
                return

            # Converte para dict e adiciona timestamp se necessário
            metrics_dict = asdict(metrics)
            if not metrics_dict.get('timestamp'):
                metrics_dict['timestamp'] = datetime.now(timezone.utc).isoformat()

            # Adiciona à fila
            try:
                self.log_queue.put_nowait(metrics_dict)
            except queue.Full:
                self.stats['logs_dropped'] += 1
                logger.warning(f"Fila de logs cheia - descartando log {metrics.decision_id}")

        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Erro ao registrar métricas: {e}")

    def _is_hold_decision(self, metrics: TradingDecisionMetrics) -> bool:
        """
        Verifica se a decisão de trading é uma posição HOLD.

        Args:
            metrics: Métricas da decisão de trading

        Returns:
            True se for uma posição HOLD, False caso contrário
        """
        try:
            # Verifica se o sinal consolidado é HOLD
            if hasattr(metrics, 'consolidated_metrics') and metrics.consolidated_metrics:
                if metrics.consolidated_metrics.final_signal.upper() == 'HOLD':
                    return True

            # Verifica se o execution_reason indica HOLD
            if hasattr(metrics, 'execution_reason') and metrics.execution_reason:
                if 'HOLD' in metrics.execution_reason.upper():
                    return True

            # Verifica se não foi executado trade e não há rejection_reason (indica HOLD)
            if (hasattr(metrics, 'trade_executed') and not metrics.trade_executed and
                hasattr(metrics, 'rejection_reason') and not metrics.rejection_reason):
                # Se não foi executado e não há razão de rejeição, provavelmente é HOLD
                return True

            # Verifica se todos os timeframes indicam HOLD
            if hasattr(metrics, 'timeframe_metrics') and metrics.timeframe_metrics:
                hold_count = sum(1 for tf in metrics.timeframe_metrics
                               if tf.signal.upper() == 'HOLD')
                if hold_count == len(metrics.timeframe_metrics):
                    return True

            return False

        except Exception as e:
            logger.warning(f"Erro ao verificar se decisão é HOLD: {e}")
            return False

    def _processing_loop(self):
        """Loop principal de processamento de logs."""
        logger.info("Loop de processamento de métricas iniciado")
        
        while not self.stop_event.is_set():
            try:
                # Processa logs em batch
                logs_to_write = []
                
                # Coleta logs por até flush_interval segundos
                end_time = time.time() + self.flush_interval
                
                while time.time() < end_time and not self.stop_event.is_set():
                    try:
                        timeout = max(0.1, end_time - time.time())
                        log_entry = self.log_queue.get(timeout=timeout)
                        logs_to_write.append(log_entry)
                        self.log_queue.task_done()
                    except queue.Empty:
                        break
                
                # Escreve logs se houver algum
                if logs_to_write:
                    self._write_logs_batch(logs_to_write)
                
            except Exception as e:
                self.stats['errors'] += 1
                logger.error(f"Erro no loop de processamento: {e}")
                time.sleep(1.0)  # Evita loop infinito em caso de erro
        
        logger.info("Loop de processamento de métricas finalizado")
    
    def _write_logs_batch(self, logs: List[Dict[str, Any]]):
        """Escreve um batch de logs no arquivo."""
        try:
            # Verifica se precisa rotacionar arquivo
            current_date = datetime.now().date()
            if current_date != self.current_date:
                self._rotate_log_file(current_date)
            
            # Escreve logs
            for log_entry in logs:
                json_line = json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))
                self.current_file.write(json_line + '\n')
                self.stats['logs_written'] += 1
            
            # Força flush
            self.current_file.flush()
            os.fsync(self.current_file.fileno())
            
            self.stats['last_flush'] = datetime.now().isoformat()
            
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Erro ao escrever batch de logs: {e}")
    
    def _rotate_log_file(self, date):
        """Rotaciona arquivo de log diário."""
        try:
            # Fecha arquivo anterior
            if self.current_file:
                self.current_file.close()
            
            # Cria novo arquivo
            filename = f"trading_metrics_{date.strftime('%Y%m%d')}.jsonl"
            filepath = self.log_directory / filename
            
            self.current_file = open(filepath, 'a', encoding='utf-8')
            self.current_date = date
            
            logger.info(f"Arquivo de log rotacionado: {filepath}")
            
            # Comprime arquivo anterior se habilitado
            if self.enable_compression:
                self._compress_old_files()
        
        except Exception as e:
            logger.error(f"Erro ao rotacionar arquivo de log: {e}")
    
    def _compress_old_files(self):
        """Comprime arquivos de log antigos."""
        try:
            import gzip

            # Obtém o path do arquivo atual para comparação segura
            current_file_path = None
            if self.current_file and hasattr(self.current_file, 'name'):
                current_file_path = Path(self.current_file.name)

            # Procura arquivos .jsonl de dias anteriores
            for file_path in self.log_directory.glob("trading_metrics_*.jsonl"):
                # YAA-FIX: Comparação correta de paths para evitar WinError 32
                if current_file_path and file_path.resolve() == current_file_path.resolve():
                    logger.debug(f"Pulando arquivo atual: {file_path}")
                    continue  # Não comprime arquivo atual

                # Verifica se já existe versão comprimida
                gz_path = file_path.with_suffix('.jsonl.gz')
                if gz_path.exists():
                    logger.debug(f"Arquivo já comprimido: {gz_path}")
                    continue

                # YAA-FIX: Verifica se o arquivo não está sendo usado antes de comprimir
                try:
                    # Tenta abrir o arquivo em modo exclusivo para verificar se está livre
                    with open(file_path, 'r+b') as test_file:
                        pass
                except (PermissionError, OSError) as e:
                    logger.warning(f"Arquivo {file_path} ainda em uso, pulando compressão: {e}")
                    continue

                # Comprime arquivo
                logger.info(f"Comprimindo arquivo: {file_path}")
                with open(file_path, 'rb') as f_in:
                    with gzip.open(gz_path, 'wb') as f_out:
                        f_out.writelines(f_in)

                # Remove arquivo original
                file_path.unlink()
                logger.info(f"Arquivo comprimido e removido: {gz_path}")

        except Exception as e:
            logger.error(f"Erro ao comprimir arquivos antigos: {e}")
    
    def _flush_remaining_logs(self):
        """Processa logs restantes na fila."""
        remaining_logs = []
        
        try:
            while True:
                log_entry = self.log_queue.get_nowait()
                remaining_logs.append(log_entry)
                self.log_queue.task_done()
        except queue.Empty:
            pass
        
        if remaining_logs:
            logger.info(f"Processando {len(remaining_logs)} logs restantes")
            self._write_logs_batch(remaining_logs)
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do logger."""
        return {
            **self.stats,
            'queue_size': self.log_queue.qsize(),
            'is_running': self.is_running,
            'current_file': str(self.current_file.name) if self.current_file else None
        }
    
    @contextmanager
    def measure_processing_time(self):
        """Context manager para medir tempo de processamento."""
        start_time = time.perf_counter()
        try:
            yield
        finally:
            end_time = time.perf_counter()
            processing_time = (end_time - start_time) * 1000  # em ms
            return processing_time

# Instância global do logger
_global_logger: Optional[RealTimeMetricsLogger] = None

def get_metrics_logger(filter_hold_positions: bool = True) -> RealTimeMetricsLogger:
    """
    Retorna a instância global do logger de métricas.

    Args:
        filter_hold_positions: Se deve filtrar posições HOLD dos logs
    """
    global _global_logger

    if _global_logger is None:
        _global_logger = RealTimeMetricsLogger(filter_hold_positions=filter_hold_positions)
        _global_logger.start()

    return _global_logger

def shutdown_metrics_logger():
    """Para o logger global de métricas."""
    global _global_logger
    
    if _global_logger:
        _global_logger.stop()
        _global_logger = None

# Funções de conveniência
def create_timeframe_metrics(timeframe: str, signal_data: Dict[str, Any]) -> TimeframeMetrics:
    """Cria métricas de timeframe a partir de dados de sinal."""
    return TimeframeMetrics(
        timeframe=timeframe,
        signal=signal_data.get('signal', 'hold'),
        confidence=signal_data.get('confidence', 0.0),
        signal_strength=signal_data.get('signal_strength', 0.0),
        hype_momentum=signal_data.get('hype_momentum', 0.0),
        holographic_boost=signal_data.get('holographic_boost', 1.0),
        tsvf_validation=signal_data.get('tsvf_validation', 0.5),
        data_periods=signal_data.get('data_periods', 0),
        timestamp=datetime.now(timezone.utc).isoformat()
    )

def create_consolidated_metrics(consolidated_signal: Any) -> ConsolidatedMetrics:
    """Cria métricas consolidadas a partir de sinal consolidado."""
    return ConsolidatedMetrics(
        final_signal=consolidated_signal.signal,
        final_confidence=consolidated_signal.confidence,
        convergence_score=consolidated_signal.convergence_score,
        primary_timeframe=consolidated_signal.primary_timeframe,
        supporting_timeframes=consolidated_signal.supporting_timeframes,
        reasoning=consolidated_signal.reasoning,
        timestamp=datetime.now(timezone.utc).isoformat()
    )

def log_individual_timeframe_signal(
    decision_id: str,
    symbol: str,
    current_price: float,
    timeframe_signal: Any,
    **kwargs
) -> str:
    """Registra um sinal individual de timeframe para tracking."""
    
    # Cria métricas do timeframe
    tf_metrics = TimeframeMetrics(
        timeframe=timeframe_signal.timeframe,
        signal=timeframe_signal.signal,
        confidence=timeframe_signal.confidence,
        signal_strength=timeframe_signal.signal_strength,
        hype_momentum=timeframe_signal.hype_momentum,
        holographic_boost=timeframe_signal.holographic_boost,
        tsvf_validation=timeframe_signal.tsvf_validation,
        data_periods=kwargs.get('data_periods', 0),
        timestamp=timeframe_signal.timestamp.isoformat() if hasattr(timeframe_signal.timestamp, 'isoformat') else str(timeframe_signal.timestamp)
    )
    
    # Cria métricas consolidadas vazias para timeframe individual
    consolidated_metrics = ConsolidatedMetrics(
        final_signal=timeframe_signal.signal,
        final_confidence=timeframe_signal.confidence,
        convergence_score=0.0,
        primary_timeframe=timeframe_signal.timeframe,
        supporting_timeframes=[],
        reasoning=f"Sinal individual do timeframe {timeframe_signal.timeframe}",
        timestamp=datetime.now(timezone.utc).isoformat()
    )
    
    # Cria métricas da decisão para tracking
    decision_metrics = TradingDecisionMetrics(
        decision_id=f"{decision_id}_{timeframe_signal.timeframe}",
        symbol=symbol,
        timestamp=datetime.now(timezone.utc).isoformat(),
        log_type="SIGNAL_TRACKING",
        current_price=current_price,
        volume=kwargs.get('volume'),
        timeframe_metrics=[tf_metrics],
        consolidated_metrics=consolidated_metrics,
        trade_executed=False,
        execution_reason="Tracking de sinal individual",
        rejection_reason=None,
        stop_loss=None,
        take_profit=None,
        position_size=None,
        risk_percentage=None,
        processing_time_ms=kwargs.get('processing_time_ms', 0.0),
        data_quality_score=kwargs.get('data_quality_score', 1.0),
        market_conditions=kwargs.get('market_conditions', {}),
        system_state=kwargs.get('system_state', {})
    )
    
    # Registra no logger
    metrics_logger = get_metrics_logger()
    metrics_logger.log_trading_decision(decision_metrics)
    
    return decision_metrics.decision_id

def log_trading_decision(
    decision_id: str,
    symbol: str,
    current_price: float,
    timeframe_signals: List[Any],
    consolidated_signal: Any,
    trade_executed: bool,
    execution_reason: str,
    rejection_reason: Optional[str] = None,
    log_type: str = "TRADE_EXECUTION",
    **kwargs
) -> str:
    """Função de conveniência para registrar uma decisão de trading completa."""
    
    # Cria métricas de timeframes
    timeframe_metrics = []
    for tf_signal in timeframe_signals:
        tf_metrics = TimeframeMetrics(
            timeframe=tf_signal.timeframe,
            signal=tf_signal.signal,
            confidence=tf_signal.confidence,
            signal_strength=tf_signal.signal_strength,
            hype_momentum=tf_signal.hype_momentum,
            holographic_boost=tf_signal.holographic_boost,
            tsvf_validation=tf_signal.tsvf_validation,
            data_periods=kwargs.get('data_periods', 0),
            timestamp=tf_signal.timestamp.isoformat() if hasattr(tf_signal.timestamp, 'isoformat') else str(tf_signal.timestamp)
        )
        timeframe_metrics.append(tf_metrics)
    
    # Cria métricas consolidadas
    consolidated_metrics = create_consolidated_metrics(consolidated_signal)
    
    # Cria métricas completas da decisão
    decision_metrics = TradingDecisionMetrics(
        decision_id=decision_id,
        symbol=symbol,
        timestamp=datetime.now(timezone.utc).isoformat(),
        log_type=log_type,
        current_price=current_price,
        volume=kwargs.get('volume'),
        timeframe_metrics=timeframe_metrics,
        consolidated_metrics=consolidated_metrics,
        trade_executed=trade_executed,
        execution_reason=execution_reason,
        rejection_reason=rejection_reason,
        stop_loss=kwargs.get('stop_loss'),
        take_profit=kwargs.get('take_profit'),
        position_size=kwargs.get('position_size'),
        risk_percentage=kwargs.get('risk_percentage'),
        processing_time_ms=kwargs.get('processing_time_ms', 0.0),
        data_quality_score=kwargs.get('data_quality_score', 1.0),
        market_conditions=kwargs.get('market_conditions', {}),
        system_state=kwargs.get('system_state', {})
    )
    
    # Registra no logger
    metrics_logger = get_metrics_logger()
    metrics_logger.log_trading_decision(decision_metrics)
    
    return decision_id