{"log_level": "INFO", "verbose_ticker_logging": false, "dedup_interval": 10.0, "handlers": {"console": {"level": "INFO"}, "rotating_file": {"level": "DEBUG", "filename": "logs/qualia.log", "maxBytes": 10485760, "backupCount": 5}}, "module_levels": {"src.qualia.market.base_integration": "INFO", "qualia.market": "INFO", "qualia.strategies": "INFO", "qualia.consciousness": "INFO"}, "suppress_patterns": ["HOLD signal", "Posição mantida", "Aguardando próximo ciclo", "<PERSON><PERSON> hit", "Rate limiting: aguardando"], "tracing": {"service_name": "qualia-market", "exporter": "console"}, "metrics": {"filter_hold_positions": true, "log_directory": "logs/metrics", "max_queue_size": 1000, "flush_interval": 1.0, "enable_compression": true}}