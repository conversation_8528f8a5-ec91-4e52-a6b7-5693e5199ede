# QUALIA Hyperparameters Configuration
# Arquivo central para todos os hiperparâmetros do sistema
# YAA-FIX: Valores consolidados do sistema unificado YAML

# Parâmetros de Amplificação
price_amplification: 1.0  # <PERSON>or otimizado (-51.4% impact)
news_amplification: 8.0  # <PERSON><PERSON> otimizado (+463.2% impact)

# Thresholds de Confiança
min_confidence: 0.37  # <PERSON><PERSON> otimizado (-7.0% impact)
pattern_threshold: 0.3  # Threshold para detecção de padrões

# Configurações de Trading
max_positions: 5
position_size: 0.1
stop_loss: 0.02
take_profit: 0.03

# Configurações de Risco
max_drawdown: 0.05
risk_per_trade: 0.01
volatility_adjustment: 1.0

# Configurações de Mercado
low_coherence_threshold: 0.3
high_coherence_threshold: 0.7
market_regime_threshold: 0.5

# Configurações de Consciência
consciousness_threshold: 0.6
quantum_coherence_min: 0.4
holographic_field_strength: 0.8

# Configurações de Memória
memory_decay_rate: 0.1
pattern_memory_size: 1000
experience_replay_size: 500

# Configurações de Otimização
learning_rate: 0.001
momentum: 0.9
adaptive_threshold: true
auto_calibration: true

# Configurações de Monitoramento
metrics_interval: 60
logging_level: "INFO"
enable_telemetry: true

# Configurações Específicas por Símbolo
symbol_configs:
  BTCUSDT:
    weight: 0.3
    min_confidence_override: 0.3
    volatility_adjustment: 1.0
  ETHUSDT:
    weight: 0.25
    min_confidence_override: 0.4
    volatility_adjustment: 1.1
  BNBUSDT:
    weight: 0.15
    min_confidence_override: 0.5
    volatility_adjustment: 1.2
  ADAUSDT:
    weight: 0.1
    min_confidence_override: 0.6
    volatility_adjustment: 1.3
  SOLUSDT:
    weight: 0.1
    min_confidence_override: 0.5
    volatility_adjustment: 1.4
  DOTUSDT:
    weight: 0.05
    min_confidence_override: 0.6
    volatility_adjustment: 1.2
  LINKUSDT:
    weight: 0.03
    min_confidence_override: 0.7
    volatility_adjustment: 1.3
  POLUSDT:
    weight: 0.02
    min_confidence_override: 0.7
    volatility_adjustment: 1.5

# Configurações de Ambiente
environment:
  mode: "production"
  debug: false
  verbose_logging: false
  enable_paper_trading: true
  enable_live_trading: false

# Configurações de Performance
performance:
  enable_numba: true
  parallel_processing: true
  cache_enabled: true
  optimization_level: 2